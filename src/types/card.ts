// src/types/card.ts
// export interface Card {
//     id: string;
//     title: string;
//     description: string;
//     status: 'active' | 'inactive' | 'pending';
//     image: string;
//     templateId: string;
//     createdAt: string;
//     updatedAt: string;
// }

export type CardInput = Omit<Card, 'id' | 'createdAt' | 'updatedAt'>;

export interface Card {
    id: string;
    name: string;
    subHeader: string;
    description: string;
    imageUrl: File;
    utmLink: string;
    ruleId: string;
    earning: string;
    status: 'active' | 'inactive' | 'pending';
    benefits: string[];
    createdAt: string;
    updatedAt: string;
}

export interface CreateCardData {
    name: string;
    subHeader: string;
    description: string;
    imageUrl: File;
    utmLink: string;
    ruleId: string;
    earning: string;
    status: 'active' | 'inactive' | 'pending';
    benefits: string[];
}

export interface UpdateCardData {
    name?: string;
    subHeader?: string;
    description?: string;
    imageUrl?: File;
    utmLink?: string;
    ruleId?: string;
    earning?: string;
    status?: 'active' | 'inactive' | 'pending';
    benefits?: string[];
}

export interface CardState {
    cards: Card[];
    isLoading: boolean;
    error: Error | null;
    totalCount: number,
    fetchCards: (page?: number, limit?: number) => Promise<void>;
    createCard: (data: CreateCardData) => Promise<Card>;
    updateCard: (id: string, data: UpdateCardData) => Promise<Card>;
    deleteCard: (id: string) => Promise<void>;
    updateCardStatus: (id: string, status: Card['status']) => Promise<void>;
}