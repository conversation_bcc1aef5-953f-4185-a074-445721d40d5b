// themes/stepper.ts
const baseStyle = {
  // Style the indicator part
  indicator: {
    // Use brand.900 for all states
    // bg: 'brand.900',
    // borderColor: 'brand.900',
    // color: 'white',
    // Keep rounded indicators, but you can set borderRadius: 0 for square
    // borderRadius: 'full',
    
    // Override specific states to maintain brand.900
    '&[data-status=active]': {
      bg: 'brand.900',
      borderColor: 'brand.900',
      color: 'white',
    },
    '&[data-status=complete]': {
      bg: 'brand.900',
      borderColor: 'brand.900',
      color: 'white',
    },
    _incomplete: {
      bg: 'brand.900',
      borderColor: 'brand.900',
      color: 'brand.900',
    },
  },
  
  // Style the step title
  title: {
    color: 'brand.900',
    fontWeight: 'medium',
  },
  
  // Style the step description
  description: {
    color: 'gray.600',
  },
  
  // Style the separator (line between steps)
  separator: {
    bg: 'brand.900',
    _complete: {
      bg: 'brand.900',
    },
    _incomplete: {
      bg: 'gray.300',
    },
  },
}

const Stepper = {
  baseStyle,
}

export default Stepper;