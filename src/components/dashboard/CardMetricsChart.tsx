import React from 'react';
import {
  Box,
  Flex,
  Text,
  Skeleton,
} from '@chakra-ui/react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XA<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  ResponsiveContainer,
} from 'recharts';

// Type for card metrics data
interface CardMetric {
  name: string;
  approved: number;
  total: number;
}

interface CardMetricsChartProps {
  data: CardMetric[];
  isLoading: boolean;
}

// Custom rounded bar shape component with semicircular ends
export const RoundedBar = (props: any) => {
  const { fill, x, y, width, height, payload, layout = 'horizontal' } = props;
  
  if (layout === 'vertical') {
    // For vertical bars, semicircles on top and bottom
    const radius = width / 2;
    
    // If height is less than width, make it fully rounded
    if (height <= width) {
      return (
        <circle 
          cx={x + width / 2} 
          cy={y + height / 2} 
          r={Math.min(width, height) / 2} 
          fill={fill} 
        />
      );
    }
    
    // Create path with semicircular ends for vertical bars
    const path = `
      M${x},${y + radius}
      A${radius},${radius} 0 0,1 ${x + width},${y + radius}
      L${x + width},${y + height - radius}
      A${radius},${radius} 0 0,1 ${x},${y + height - radius}
      Z
    `;

    return <path d={path} fill={fill} />;
  } else {
    // For horizontal bars, semicircles on left and right
    const radius = height / 2;
    
    // If width is less than height, make it fully rounded
    if (width <= height) {
      return (
        <circle 
          cx={x + width / 2} 
          cy={y + height / 2} 
          r={Math.min(width, height) / 2} 
          fill={fill} 
        />
      );
    }
    
    // Create path with semicircular ends for horizontal bars
    const path = `
      M${x + radius},${y}
      L${x + width - radius},${y}
      A${radius},${radius} 0 0,1 ${x + width - radius},${y + height}
      L${x + radius},${y + height}
      A${radius},${radius} 0 0,1 ${x + radius},${y}
      Z
    `;

    return <path d={path} fill={fill} />;
  }
};

const CardMetricsChart: React.FC<CardMetricsChartProps> = ({ data, isLoading }) => {
  if (isLoading) {
    return (
      <Skeleton height="200px" />
    );
  }

  return (
    <Box height="280px">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          layout="vertical"
          data={data}
          margin={{ top: 5, right: 10, left: 10, bottom: 5 }}
          barCategoryGap={25}
          barGap={10}
        >
          <XAxis 
            type="number" 
            domain={[0, 'dataMax']} 
            axisLine={true} 
            tickLine={true}
          />
          <YAxis 
            type="category" 
            dataKey="name" 
            tick={{ fontSize: 14 }}
            width={60}
            axisLine={false}
            tickLine={false}
          />
          <Bar 
            dataKey="total" 
            fill="#1A4A9E" 
            radius={[0, 4, 4, 0]} 
            shape={<RoundedBar />}
            // barSize={16}
            name="Total"
          />
          <Bar 
            dataKey="approved" 
            fill="#68D391" 
            radius={[0, 4, 4, 0]} 
            shape={<RoundedBar />}
            // barSize={16}
            name="Approved"
          />
        </BarChart>
      </ResponsiveContainer>
    </Box>
  );
};

export default CardMetricsChart;