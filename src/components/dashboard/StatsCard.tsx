import React, { useState } from 'react';
import {
  Box,
  Flex,
  Stat,
  StatLabel,
  StatNumber,
  useColorModeValue,
  Skeleton,
  Image,
  Icon,
} from '@chakra-ui/react';
import { FiFileText } from 'react-icons/fi'; // Fallback icon

interface StatsCardProps {
  title: string;
  stat: string;
  icon: React.ReactNode; // Icon/Image passed from parent
  trendImage?: React.ReactNode; // Image component passed from parent
  trendColor?: string;
  isLoading?: boolean;
}

const StatsCard: React.FC<StatsCardProps> = ({ 
  title, 
  stat, 
  icon,
  trendImage,
  trendColor = "blue",
  isLoading = false,
}) => {
  return (
    <Stat
      px={6}
      py={5}
      bg="white"
      borderRadius="lg"
      boxShadow="sm"
      position="relative"
    >
      <Flex justifyContent="space-between">
        <Box>
          <Flex align="center">
            <Box
              mr={4}
              borderRadius="full"
              bg={`${trendColor}.50`}
              p={0}
              color={`${trendColor}.500`}
              minW="56px"
              minH="56px"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              {icon}
            </Box>
            <Box>
              <StatLabel fontWeight="medium" fontSize="md" color="gray.500" isTruncated>
                {title}
              </StatLabel>
              <Skeleton isLoaded={!isLoading} mt={1}>
                <StatNumber fontSize="3xl" fontWeight="bold">
                  {stat}
                </StatNumber>
              </Skeleton>
            </Box>
          </Flex>
        </Box>
      </Flex>
      
      {/* Trend Image Area */}
      {trendImage && (
        <Skeleton isLoaded={!isLoading} mt={4} h="50px">
          <Box position="relative" height="50px" mt={4}>
            {trendImage}
          </Box>
        </Skeleton>
      )}
    </Stat>
  );
};

export default StatsCard;