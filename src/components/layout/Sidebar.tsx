// src/components/layout/Sidebar.tsx
import {
  Box,
  VStack,
  Icon,
  Text,
  Flex,
  Tooltip,
  Image
} from '@chakra-ui/react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  FiClock
} from 'react-icons/fi';
import { HomeIcon, ShieldIcon, UsersIcon, CardsIcon, DocumentIcon } from '../icons';
import { useAuthStore } from '../../store/authStore';

interface SidebarLinkProps {
  icon: React.ElementType;
  children: React.ReactNode;
  to: string;
}

const SidebarLink = ({ icon: IconComponent, children, to }: SidebarLinkProps) => {
  const location = useLocation();
  const isActive = location.pathname === to || location.pathname.startsWith(`${to}/`);

  return (
    <Tooltip label={children} placement="right" hasArrow openDelay={500} display={{ base: 'block', md: 'none' }}>
      <Box
        as={NavLink}
        to={to}
        w="full"
        borderRadius="lg"
        transition="all 0.3s"
        py={3}
        px={4}
        mb={2}
        bg={isActive ? 'brand.50' : 'transparent'}
        color={isActive ? 'brand.500' : 'gray.600'}
        fontWeight={isActive ? 'semibold' : 'medium'}
        position="relative"
        _before={{
          content: '""',
          position: 'absolute',
          left: isActive ? '0' : '-4px',
          top: '50%',
          transform: 'translateY(-50%)',
          width: '4px',
          height: '60%',
          bg: 'brand.500',
          borderRadius: '0 4px 4px 0',
          transition: 'all 0.3s',
          opacity: isActive ? 1 : 0,
        }}
        _hover={{
          bg: isActive ? 'brand.50' : 'gray.50',
          color: isActive ? 'brand.500' : 'brand.500',
          _before: {
            left: '0',
            opacity: 0.7,
          }
        }}
      >
        <Flex align="center">
          {/* Remove Icon wrapper, use the icon component directly */}
          <IconComponent
            boxSize="24px"
            mr={3}
            color={isActive ? 'brand.500' : 'gray.500'}
          />
          <Text fontSize="sm">{children}</Text>
        </Flex>
      </Box>
    </Tooltip>
  );
};

// Rest of your component remains the same...

const Sidebar = () => {
  const user = useAuthStore((state) => state.user);
  const permissions = user?.permissions || [];

  // Helper to check if a module can be viewed
  const canView = (moduleName: string) => {
    return permissions.find(p => p.module_name === moduleName)?.can_view;
  };

  return (
    <Box
      as="aside"
      w="260px"
      bg="white" // White background as per mockup
      borderRight="none" // Remove border
      h="100vh"
      position="sticky"
      top={0}
      zIndex={10}
      boxShadow="0 0 20px rgba(0, 0, 0, 0.05)" // Subtle shadow instead of border
      overflowY="auto"
      css={{
        '&::-webkit-scrollbar': {
          width: '4px',
        },
        '&::-webkit-scrollbar-track': {
          width: '6px',
        },
        '&::-webkit-scrollbar-thumb': {
          background: '#CBD5E0', // Gray scrollbar
          borderRadius: '24px',
        },
      }}
    >
      {/* Logo and Brand */}
      <Flex
        direction="column"
        align="center"
        justify="center"
        py={4}
        height="102px"
        borderBottom="none"
        bg="white" // White header as per mockup
        position="relative"
        _after={{
          content: '""',
          position: 'absolute',
          bottom: 0,
          left: '10%',
          width: '80%',
          height: '1px',
          bg: 'gray.100',
        }}
      >
        <Box
          w="140px"
          display="flex"
          alignItems="center"
          justifyContent="center"
          p={2}
          borderRadius="md"
          transition="all 0.3s ease"
        >
          <Image
            src="/src/assets/rionet_logo.png"
            alt="Rionet Logo"
            maxW="160px"
            h="auto"
            objectFit="contain"
          />
        </Box>
      </Flex>

      {/* Navigation Links */}
      <Box p={4}>
        <VStack spacing={1} align="stretch">
          {canView('dashboard') && (
            <SidebarLink to="/" icon={HomeIcon}>Dashboard</SidebarLink>
          )}
          {canView('applications') && (
            <SidebarLink to="/applications" icon={ShieldIcon}>Applications</SidebarLink>
          )}
          {canView('users') && (
            <SidebarLink to="/users" icon={UsersIcon}>User Management</SidebarLink>
          )}
          {canView('cards') && (
            <SidebarLink to="/cards" icon={CardsIcon}>Card Management</SidebarLink>
          )}
          {canView('rule') && (
            <SidebarLink to="/rules" icon={DocumentIcon}>Rule Management</SidebarLink>
          )}
        </VStack>
      </Box>

      {/* User Info at Bottom */}
      {/* <Box
        bottom={0}
        left={0}
        right={0}
        p={4}
        borderTop="none"
        bg="white" // White background
        position="absolute"
        _before={{
          content: '""',
          position: 'absolute',
          top: 0,
          left: '10%',
          width: '80%',
          height: '1px',
          bg: 'gray.100',
        }}
      >
        <Flex align="center">
          <Box
            bg="brand.50" // Very light blue background
            borderRadius="full"
            p={2}
            mr={3}
            boxShadow="0 2px 4px rgba(25, 74, 156, 0.1)" // Subtle shadow with brand color
          >
            <Icon as={FiClock} color="brand.500" />
          </Box>
          <Box>
            <Text fontSize="xs" color="gray.500">Last login</Text>
            <Text fontSize="xs" fontWeight="medium" color="gray.700">Today, 10:30 AM</Text>
          </Box>
        </Flex>
      </Box> */}
    </Box>
  );
  };

  export default Sidebar;