import { useEffect } from 'react';
import {
  Box,
  Grid,
  GridItem,
  Heading,
  Text,
  Flex,
  Icon,
  Select,
  Image,
} from '@chakra-ui/react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  Legend,
  Cell,
} from 'recharts';
import { FiFileText, FiCheckCircle } from 'react-icons/fi';

// Store
import { useDashboardStore } from '../store/dashboardStore';

// Components
import StatsCard from '../components/dashboard/StatsCard';
import CardMetricsChart, { RoundedBar } from '../components/dashboard/CardMetricsChart';

const Dashboard = () => {
  const {
    isLoading,
    error,
    totalApplications,
    totalApproved,
    applicationTrend,
    approvalTrend,
    applicationMetrics,
    cardMetrics,
    fetchDashboardData,
    timeframe,
    setTimeframe
  } = useDashboardStore();

  // Fetch dashboard data on mount
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Handle time period change
  const handleTimeframeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setTimeframe(e.target.value as 'daily' | 'weekly' | 'monthly' | '3months');
  };

  if (error) {
    return (
      <Box textAlign="center" py={10} px={6}>
        <Heading as="h2" size="xl" color="red.500">
          Error Loading Dashboard
        </Heading>
        <Text mt={4}>{error}</Text>
      </Box>
    );
  }

  return (
    <Box>
      {/* Main Layout Grid - Stats Cards and Card Metrics Chart side by side */}
      <Grid templateColumns={{ base: "1fr", md: "280px 1fr" }} gap={6} mb={6}>
        {/* Left Column - Vertically Stacked Stats Cards */}
        <GridItem>
          <Flex direction="column" gap={6}>
            {/* Total Applications Card */}
            <StatsCard
              title="Total Applications"
              stat={totalApplications.toString()}
              icon={<Image src="/src/assets/blue_shield.png" alt="Rionet Logo" maxW="50px" h="auto" objectFit="contain" />}
              trendImage={<Image src="/src/assets/blue_line_graph.png" alt="Trend" w="100%" h="50px" objectFit="contain" />}
              trendColor="blue"
              isLoading={isLoading}
            />

            {/* Total Approved Card */}
            <StatsCard
              title="Total Approved"
              stat={totalApproved.toString()}
              icon={<Image src="/src/assets/green_shield.png" alt="Rionet Logo" maxW="50px" h="auto" objectFit="contain" />}
              trendImage={<Image src="/src/assets/green_line_graph.png" alt="Trend" w="100%" h="50px" objectFit="contain" />}
              trendColor="green"
              isLoading={isLoading}
            />
          </Flex>
        </GridItem>

        {/* Right Column - Card Metrics Chart */}
        <GridItem>
          <Box
            bg="white"
            borderRadius="xl"
            boxShadow="sm"
            p={6}
            h="100%"
            border="1px"
            borderColor="gray.100"
          >
            <Heading as="h2" size="md" mb={6} color="gray.700">
              Card Metric
            </Heading>

            <CardMetricsChart data={cardMetrics} isLoading={isLoading} />
          </Box>
        </GridItem>
      </Grid>

      {/* Application Metrics Chart - Full Width Below */}
      <Box
        bg="white"
        borderRadius="xl"
        boxShadow="sm"
        p={6}
        border="1px"
        borderColor="gray.100"
      >
        <Flex justify="space-between" align="center" mb={6}>
          <Heading as="h2" size="md" color="gray.700">
            Total Application Metrics
          </Heading>

          <Flex align="center" gap={4}>
            {/* Legend */}
            <Flex align="center" gap={4} mr={4}>
              <Flex align="center" gap={2}>
                <Box w={3} h={3} borderRadius="full" bg="#1A4A9E" />
                <Text fontSize="sm" color="gray.600">Application</Text>
              </Flex>
              <Flex align="center" gap={2}>
                <Box w={3} h={3} borderRadius="full" bg="#14BEF1" />
                <Text fontSize="sm" color="gray.600">Approved</Text>
              </Flex>
            </Flex>

            <Select
              value={timeframe}
              onChange={handleTimeframeChange}
              w="120px"
              size="sm"
              borderRadius="md"
              border="1px"
              borderColor="gray.300"
              fontSize="sm"
            >
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="3months">Past 3 Months</option>
            </Select>
          </Flex>
        </Flex>

        {isLoading ? (
          <Flex justify="center" align="center" h="300px">
            <Text>Loading chart data...</Text>
          </Flex>
        ) : (
          <ResponsiveContainer width="100%" height={320}>
            <BarChart 
              data={applicationMetrics}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              barCategoryGap="25%"
              barGap="15%"
            >
              <CartesianGrid 
                strokeDasharray="3 3" 
                horizontal={true} 
                vertical={false}
                stroke="#E2E8F0"
              />
              <XAxis 
                dataKey="name" 
                axisLine={false} 
                tickLine={false}
                tick={{ fontSize: 12, fill: '#718096' }}
                dy={10}
              />
              <YAxis 
                axisLine={false} 
                tickLine={false}
                tick={{ fontSize: 12, fill: '#718096' }}
                domain={[0, 'dataMax']}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #E2E8F0',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
              />
              <Bar 
                dataKey="application" 
                name="Application" 
                fill="#1A4A9E"
                shape={<RoundedBar layout="vertical" />}
                // radius={[8, 8, 0, 0]}
                // maxBarSize={24}
              />
              <Bar 
                dataKey="approved" 
                name="Approved" 
                fill="#14BEF1"
                shape={<RoundedBar layout="vertical" />}
                // radius={[8, 8, 0, 0]}
                // maxBarSize={24}
              />
            </BarChart>
          </ResponsiveContainer>
        )}
      </Box>
    </Box>
  );
};

export default Dashboard;