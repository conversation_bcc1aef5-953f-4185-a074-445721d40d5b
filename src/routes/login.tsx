// src/routes/login.tsx
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Image,
  Input,
  Link,
  Text,
  VStack,
  useToast,
  FormErrorMessage,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuthStore } from '../store/authStore';

// Email/Password schema with consent checkbox
const loginSchema = z.object({
  email: z.string().min(1, 'Username is required'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  consent: z.literal(true, {
    errorMap: () => ({ message: 'You must agree to the Terms & Conditions' }),
  }),
});

type LoginFormData = z.infer<typeof loginSchema>;

const Login = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const login = useAuthStore((state) => state.login);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: 'onChange',
    defaultValues: {
      email: '',
      password: '',
      consent: false,
    },
  });

  const handleLogin = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      await login(data.email, data.password);
      toast({
        title: 'Login successful',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      navigate('/');
    } catch (error) {
      toast({
        title: 'Login failed',
        description: 'Invalid email or password',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Flex minH="100vh" h="100vh" bg="gray.50">
      {/* Left image section */}
      <Box
        flex="1"
        bg="blue.50"
        display={{ base: 'none', md: 'block' }}
        h="100vh"
        position="relative"
        overflow="hidden"
      >
        <Image
          src="/src/assets/login_image.png"
          alt="Login illustration"
          w="100%"
          h="100vh"
          objectFit="cover"
          position="absolute"
          top="0"
          left="0"
        />
      </Box>

      {/* Right login form */}
      <Flex
        flex="1"
        align="center"
        bg={'white'}
        justify="center"
        p={{ base: 4, md: 8 }}
        maxW={{ base: '100%', md: '50%' }}
      >
        <VStack w="full" maxW={{ base: '100%', md: '400px' }} spacing={8}>
          {/* Logo */}
          <Image src="/src/assets/rionet_logo.png" alt="Rionet Logo" h="80px" mb={2} />

          <VStack w="full" spacing={8} as="form" onSubmit={handleSubmit(handleLogin)}>
            <VStack w="full" align="flex-start" spacing={2}>
              <Heading size="md">Welcome!</Heading>
              <Heading size={{ base: 'lg', md: 'xl' }}>Let's sign you in.</Heading>
              <Text fontSize="sm" color="gray.600">
                Enter your email and password to access your account.
              </Text>
            </VStack>

            <VStack w="full" spacing={4}>
              <FormControl isInvalid={!!errors.email}>
                <FormLabel>Email</FormLabel>
                <Input
                  type="text"
                  placeholder="<EMAIL>"
                  {...register('email')}
                />
                <FormErrorMessage>{errors.email?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.password}>
                <FormLabel>Password</FormLabel>
                <Input
                  type="password"
                  placeholder="******"
                  {...register('password')}
                />
                <FormErrorMessage>{errors.password?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.consent}>
                <Checkbox {...register('consent')}>
                  Consent +{' '}
                  <Link color="blue.500" href="#" target="_blank">
                    Terms & Conditions
                  </Link>
                </Checkbox>
                {errors.consent && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {errors.consent.message}
                  </Text>
                )}
              </FormControl>
            </VStack>

            <Button
              w="full"
              colorScheme="blue"
              type="submit"
              isLoading={isLoading}
              isDisabled={!watch('consent') || !isValid}
            >
              Sign In
            </Button>

            <Text fontSize="sm" color="gray.500" textAlign="center">
              By signing in, you agree to our{' '}
              <Link color="blue.500" href="#">
                Terms & Conditions
              </Link>
            </Text>
          </VStack>
        </VStack>
      </Flex>
    </Flex>
  );
};

export default Login;
