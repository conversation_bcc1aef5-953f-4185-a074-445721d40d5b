import React, { useEffect, useState, useCallback } from 'react';
import {
  Box, Heading, Table, Thead, Tbody, Tr, Th, Td, Button,
  Flex, Badge, useColorModeValue, Spinner, useToast
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { useApplicationStore } from '../../store/applicationStore';
import { usePagination } from '../../hooks/usePagination';
import { PaginationToolbar } from '../../components/common/PaginationToolbar';

type Application = {
  id: string;
  date: string;
  mCode: string;
  name: string;
  mobile: string;
  score: number;
  status: string;
};

const ViewApplications: React.FC = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const {
    applications,
    totalCount,
    currentPage: storePage,
    totalPages: storeTotalPages,
    isLoading,
    error,
    fetchApplications
  } = useApplicationStore();

  const [isOfflineUpdating, setIsOfflineUpdating] = useState(false);

  const hoverBg = useColorModeValue('gray.50', 'gray.700');
  const tableBg = useColorModeValue('white', 'gray.800');
  const initialPageSize = 10;

  // Initialize pagination hook
  const pagination = usePagination({
    initialPage: 1,
    initialPageSize,
    onPageChange: useCallback((page: number, pageSize: number) => {
      fetchApplications({ page, limit: pageSize });
    }, [fetchApplications])
  });

  // Sync pagination state with store
  useEffect(() => {
    pagination.setTotalItems(totalCount);
  }, [totalCount, pagination.setTotalItems]);

  // Initial load
  useEffect(() => {
    fetchApplications({ page: 1, limit: initialPageSize });
  }, []);

  // Handle errors
  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: error,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  }, [error, toast]);

  const handleViewDetails = (id: string) => {
    navigate(`/applications/${id}`);
  };

  const handleOfflineUpdate = async () => {
    setIsOfflineUpdating(true);
    try {
      // Refresh current page
      await fetchApplications({
        page: pagination.currentPage,
        limit: pagination.pageSize
      });

      toast({
        title: 'Success',
        description: 'Applications updated successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: 'Update failed',
        description: 'Failed to update applications',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsOfflineUpdating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Bureau check':
        return 'blue';
      case 'Card Offered':
        return 'green';
      case 'Application Started':
        return 'orange';
      default:
        return 'gray';
    }
  };

  return (
    <Box height="100%" display="flex" flexDirection="column">
      <Box
        bg={tableBg}
        borderRadius="2xl"
        boxShadow="md"
        height="100%"
        display="flex"
        flexDirection="column"
        overflow="hidden"
      >
        {/* Header with actions */}
        <Box p={6} borderColor="gray.200">
          <Flex justifyContent="flex-end" align="center" mb={4}>
            <Button
              colorScheme="blue"
              isLoading={isOfflineUpdating}
              loadingText="Updating"
              borderRadius="lg"
              px={6}
              fontWeight="medium"
              onClick={handleOfflineUpdate}
            >
              Offline Update
            </Button>
          </Flex>
        </Box>

        {isLoading && applications.length === 0 ? (
          <Flex justify="center" align="center" flex="1" py={10}>
            <Spinner size="xl" />
          </Flex>
        ) : (
          <Box
            flex="1"
            display="flex"
            flexDirection="column"
            minHeight="0"
            overflow="hidden"
          >
            {/* Table Container */}
            <Box flex="1" display="flex" flexDirection="column" overflow="hidden">
              {/* Fixed Header */}
              <Box overflowX="auto">
                <Table variant="simple" size="md">
                  <Thead bg={tableBg}>
                    <Tr>
                      {['Date', 'M Code', 'Name', 'Mobile', 'Score', 'Status', 'Action'].map((header) => (
                        <Th key={header} minW="120px" py={4}>
                          {header}
                        </Th>
                      ))}
                    </Tr>
                  </Thead>
                </Table>
              </Box>

              {/* Scrollable Body */}
              <Box
                flex="1"
                overflowY="auto"
                overflowX="auto"
                sx={{
                  '&::-webkit-scrollbar': {
                    width: '6px',
                    height: '6px',
                    borderRadius: '8px',
                    backgroundColor: 'rgba(0, 0, 0, 0.05)',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: 'rgba(0, 0, 0, 0.1)',
                    borderRadius: '8px',
                  },
                }}
              >
                <Table variant="simple" size="md">
                  <Tbody>
                    {applications.map((application) => (
                      <Tr
                        key={application.key}
                        _hover={{ bg: hoverBg }}
                        opacity={isLoading ? 0.6 : 1}
                      >
                        <Td minW="120px">{application.date}</Td>
                        <Td minW="120px">{application.mCode}</Td>
                        <Td minW="120px">{application.name}</Td>
                        <Td minW="120px">{application.mobile}</Td>
                        <Td minW="120px">{application.score}</Td>
                        <Td minW="120px">{application.status}</Td>
                        <Td minW="120px">
                          <Button
                            size="sm"
                            variant="outline"
                            borderRadius="md"
                            px={4}
                            fontWeight="medium"
                            onClick={() => handleViewDetails(application.id)}
                            isDisabled={isLoading}
                          >
                            Details
                          </Button>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            </Box>

            {/* Pagination Toolbar */}
            {totalCount > 0 && (
              <PaginationToolbar
                pagination={pagination}
                isLoading={isLoading}
                pageSizeOptions={[5, 10, 15, 25, 50]}
              />
            )}
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default ViewApplications;