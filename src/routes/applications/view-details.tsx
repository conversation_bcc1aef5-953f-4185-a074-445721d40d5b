import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Grid,
  Heading,
  Text,
  Flex,
  useColorModeValue,
  Spinner,
  Container,
  Step,
  StepIcon,
  StepIndicator,
  StepNumber,
  StepSeparator,
  StepStatus,
  StepTitle,
  Stepper,
  useSteps
} from '@chakra-ui/react';
import { useApplicationStore } from '../../store/applicationStore';


const ViewDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getApplicationById, isLoading } = useApplicationStore();
  
  // Color mode values
  const bgColor = useColorModeValue('#F5F5F5', 'gray.700');
  const cardBg = useColorModeValue('white', 'gray.800');
  const textPrimary = useColorModeValue('gray.900', 'white');
  const textSecondary = useColorModeValue('gray.400', 'gray.600');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Get application details from store
  const application = getApplicationById(id);

  // Define application status steps based on the mockup
  const steps = [
    { title: 'Application Started', description: '' },
    { title: 'Bureau Check', description: '' },
    { title: 'Card Offered', description: '' },
    { title: 'UTM Applied', description: '' },
    { title: 'Approved Card Out', description: '' },
    { title: 'Rejected', description: '' }
  ];

  // Determine current step - default to step 2 (Bureau Check) to match mockup
  const currentStepIndex = application?.currentStep !== undefined ? application.currentStep : 2;
  
  const { activeStep } = useSteps({
    index: currentStepIndex,
    count: steps.length,
  });

  // Redirect to applications list if ID is not found
  useEffect(() => {
    if (!isLoading && !application) {
      navigate('/applications');
    }
  }, [application, isLoading, navigate]);

  if (isLoading) {
    return (
      <Container maxW="100%">
        <Flex justify="center" align="center" height="500px">
          <Spinner size="xl" />
        </Flex>
      </Container>
    );
  }

  if (!application) {
    return null;
  }

  return (
    <Container maxW="100%">
      <Box
        bg={cardBg}
        borderRadius="xl"
        boxShadow="sm"
        border="1px"
        borderColor={borderColor}
        overflow="hidden"
      >
        {/* Header */}
        <Box p={8} pb={6}>
          <Heading size="lg" color={textPrimary} mb={8}>
            Application Details
          </Heading>

          {/* Form Fields Grid */}
          <Grid 
            templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }} 
            gap={6}
            mb={8}
          >
            {/* Full Name */}
            <Box>
              <Text fontSize="sm" color={textPrimary} mb={2} fontWeight="medium">
                Full Name
              </Text>
              <Box 
                p={2}
                minH="11"
                bg={bgColor} 
                borderRadius="lg" 
                border="1px"
                borderColor="transparent"
                display="flex"
                alignItems="center"
              >
                <Text color={textSecondary} fontWeight="medium">
                  {application.fullName || '-'}
                </Text>
              </Box>
            </Box>

            {/* Employment Type */}
            <Box>
              <Text fontSize="sm" color={textPrimary} mb={2} fontWeight="medium">
                Employment Type
              </Text>
              <Box 
                p={2}
                minH="11"
                bg={bgColor} 
                borderRadius="lg" 
                border="1px"
                borderColor="transparent"
                display="flex"
                alignItems="center"
              >
                <Text color={textSecondary} fontWeight="medium">
                  {application.employmentType || '-'}
                </Text>
              </Box>
            </Box>

            {/* Mobile */}
            <Box>
              <Text fontSize="sm" color={textPrimary} mb={2} fontWeight="medium">
                Mobile
              </Text>
              <Box 
                p={2}
                minH="11"
                bg={bgColor} 
                borderRadius="lg" 
                border="1px"
                borderColor="transparent"
                display="flex"
                alignItems="center"
              >
                <Text color={textSecondary} fontWeight="medium">
                  {application.mobile || '-'}
                </Text>
              </Box>
            </Box>

            {/* Salary Range */}
            <Box>
              <Text fontSize="sm" color={textPrimary} mb={2} fontWeight="medium">
                Salary Range
              </Text>
              <Box 
                p={2}
                minH="11"
                bg={bgColor} 
                borderRadius="lg" 
                border="1px"
                borderColor="transparent"
                display="flex"
                alignItems="center"
              >
                <Text color={textSecondary} fontWeight="medium">
                  {application.salaryRange || '-'}
                </Text>
              </Box>
            </Box>

            {/* PAN */}
            <Box>
              <Text fontSize="sm" color={textPrimary} mb={2} fontWeight="medium">
                PAN
              </Text>
              <Box 
                p={2}
                minH="11"
                bg={bgColor} 
                borderRadius="lg" 
                border="1px"
                borderColor="transparent"
                display="flex"
                alignItems="center"
              >
                <Text color={textSecondary} fontWeight="medium">
                  {application.pan || '-'}
                </Text>
              </Box>
            </Box>

            {/* Credit Score */}
            <Box>
              <Text fontSize="sm" color={textPrimary} mb={2} fontWeight="medium">
                Credit Score
              </Text>
              <Box 
                p={2}
                minH="11"
                bg={bgColor} 
                borderRadius="lg" 
                border="1px"
                borderColor="transparent"
                display="flex"
                alignItems="center"
              >
                <Text color={textSecondary} fontWeight="medium">
                  {application.creditScore || '-'}
                </Text>
              </Box>
            </Box>

            {/* Pincode */}
            <Box>
              <Text fontSize="sm" color={textPrimary} mb={2} fontWeight="medium">
                Pincode
              </Text>
              <Box 
                p={2}
                minH="11"
                bg={bgColor} 
                borderRadius="lg" 
                border="1px"
                borderColor="transparent"
                display="flex"
                alignItems="center"
              >
                <Text color={textSecondary} fontWeight="medium">
                  {application.pincode || '-'}
                </Text>
              </Box>
            </Box>

            {/* Card Applied */}
            <Box>
              <Text fontSize="sm" color={textPrimary} mb={2} fontWeight="medium">
                Card Applied
              </Text>
              <Box 
                p={2}
                minH="11"
                bg={bgColor} 
                borderRadius="lg" 
                border="1px"
                borderColor="transparent"
                display="flex"
                alignItems="center"
              >
                <Text color={textSecondary} fontWeight="medium">
                  {application.cardApplied || '-'}
                </Text>
              </Box>
            </Box>

            {/* Merchant Code */}
            <Box gridColumn={{ md: 'span 1' }}>
              <Text fontSize="sm" color={textPrimary} mb={2} fontWeight="medium">
                Merchant Code
              </Text>
              <Box 
                p={2}
                minH="11"
                bg={bgColor} 
                borderRadius="lg" 
                border="1px"
                borderColor="transparent"
                display="flex"
                alignItems="center"
              >
                <Text color={textSecondary} fontWeight="medium">
                  {application.merchantCode || '-'}
                </Text>
              </Box>
            </Box>
          </Grid>

          {/* Date & Time Section */}
          <Box mt={10}>
            <Heading size="md" color="blue.600" mb={0} fontWeight="semibold">
              Date & Time
            </Heading>

            {/* Chakra UI Stepper */}
            <Box px={4} py={6}>
              <Stepper 
                index={activeStep} 
                colorScheme="blue"
                size="lg"
                gap="0"
              >
                {steps.map((step, index) => (
                  <Step key={index}>
                    <StepIndicator>
                      <StepStatus
                        complete={<StepIcon />}
                        incomplete={<StepNumber />}
                        active={<StepNumber />}
                      />
                    </StepIndicator>
                    <Box
                      flexShrink={1}
                      minWidth={0}
                      mx="1"
                    >
                      <StepTitle
                        fontSize="xs"
                        fontWeight={index === activeStep ? "bold" : "normal"}
                        color={index === activeStep ? "blue.500" : textSecondary}
                        textAlign="center"
                        lineHeight="1.2"
                      >
                        {step.title}
                      </StepTitle>
                    </Box>
                    <StepSeparator />
                  </Step>
                ))}
              </Stepper>
            </Box>
          </Box>
        </Box>
      </Box>
    </Container>
  );
};

export default ViewDetails;