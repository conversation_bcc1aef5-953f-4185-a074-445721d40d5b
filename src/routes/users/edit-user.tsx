// src/routes/users/edit-user.tsx
import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormLabel,
  Heading,
  Input,
  SimpleGrid,
  VStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  useToast,
  Spinner,
  Center,
  Select,
  Container,
  useColorModeValue,
  Switch,
  HStack,
  Text
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useUserStore } from '../../store/userStore';

const EditUser = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const toast = useToast();

  const {
    currentUser,
    isLoading,
    error,
    fetchUserById,
    updateUser
  } = useUserStore();

  const { register, handleSubmit, formState: { errors }, reset, watch } = useForm();

  // Color mode values
  const bgColor = useColorModeValue('#F5F5F5', 'gray.700');
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.900', 'white');
  const labelColor = useColorModeValue('gray.700', 'gray.300');

  // State for role permissions - matching the mockup structure
  const [roles, setRoles] = useState({
    dashboard: { view: false, edit: false },
    users: { view: false, edit: false },
    cards: { view: false, edit: false },
    rule: { view: false, edit: false },
    applications: { view: false, edit: false },
  });

  // Fetch user data when component mounts
  useEffect(() => {
    if (id) {
      fetchUserById(id);
    }
  }, [id, fetchUserById]);

  // Set form values when user data is loaded
  useEffect(() => {
    if (currentUser) {
      reset({
        employeeCode: currentUser.employeeCode,
        fullName: currentUser.fullName,
        mobile: currentUser.mobile,
        status: currentUser.status,
      });

      setRoles(currentUser.roles);
    }
  }, [currentUser, reset]);

  // Handle errors
  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: error,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  }, [error, toast]);

  const handleRoleChange = (module: string, permission: string, checked: boolean) => {
    setRoles(prev => ({
      ...prev,
      [module]: {
        ...prev[module as keyof typeof prev],
        [permission]: checked
      }
    }));
  };

  const onSubmit = async (data: any) => {
    if (!id || !currentUser) return;

    try {
      await updateUser(id, {
        employeeCode: data.employeeCode,
        fullName: data.fullName,
        name: data.fullName, // Update name with fullName for consistency
        mobile: data.mobile,
        status: data.status,
        roles: roles,
      });

      toast({
        title: 'User updated',
        description: 'User has been successfully updated.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      navigate('/users');
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update user. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // Show loading state
  if (isLoading && !currentUser) {
    return (
      <Center h="200px">
        <Spinner size="xl" />
      </Center>
    );
  }

  // Show error state if no user found
  if (!isLoading && !currentUser && !error) {
    return (
      <Center h="200px">
        <Box textAlign="center">
          <Heading size="md">User not found</Heading>
          <Button mt={4} onClick={() => navigate('/users')}>
            Back to Users
          </Button>
        </Box>
      </Center>
    );
  }

  return (
    <Container maxW="100%" px={0}>
      <Box
        bg={cardBg}
        borderRadius="2xl"
        p={8}
        boxShadow="sm"
        minH="calc(100vh - 170px)"
      >
        <Heading size="lg" mb={8} color={textColor}>
          Edit User
        </Heading>

        <form onSubmit={handleSubmit(onSubmit)}>
          <VStack spacing={8} align="stretch">
            {/* Basic Information - Two Column Layout */}
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
              <FormControl isRequired isInvalid={!!errors.employeeCode}>
                <FormLabel color={labelColor} fontSize="sm" fontWeight="medium" mb={3}>
                  Employee Code
                </FormLabel>
                <Input
                  bg={bgColor}
                  isDisabled={true}
                  border="none"
                  borderRadius="lg"
                  h={12}
                  fontSize="sm"
                  color={textColor}
                  placeholder="Enter Employee Code"
                  _placeholder={{ color: 'gray.400' }}
                  _focus={{ 
                    bg: bgColor,
                    boxShadow: 'none',
                    borderColor: 'transparent'
                  }}
                  {...register('employeeCode', { required: true })}
                />
              </FormControl>

              <FormControl isRequired isInvalid={!!errors.mobile}>
                <FormLabel color={labelColor} fontSize="sm" fontWeight="medium" mb={3}>
                  Mobile
                </FormLabel>
                <Input
                  bg={bgColor}
                  isDisabled={true}
                  border="none"
                  borderRadius="lg"
                  h={12}
                  fontSize="sm"
                  color={textColor}
                  placeholder="Enter Mobile Number"
                  _placeholder={{ color: 'gray.400' }}
                  _focus={{ 
                    bg: bgColor,
                    boxShadow: 'none',
                    borderColor: 'transparent'
                  }}
                  {...register('mobile', {
                    required: true,
                    pattern: {
                      value: /^\d{10}$/,
                      message: 'Please enter a valid 10-digit mobile number'
                    }
                  })}
                />
              </FormControl>
            </SimpleGrid>

            {/* Second Row - Full Name and User Status */}
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
              <FormControl isRequired isInvalid={!!errors.fullName}>
                <FormLabel color={labelColor} fontSize="sm" fontWeight="medium" mb={3}>
                  Full Name
                </FormLabel>
                <Input
                  bg={bgColor}
                  isDisabled={true}
                  border="none"
                  borderRadius="lg"
                  h={12}
                  fontSize="sm"
                  color={textColor}
                  placeholder="Enter Full Name"
                  _placeholder={{ color: 'gray.400' }}
                  _focus={{ 
                    bg: bgColor,
                    boxShadow: 'none',
                    borderColor: 'transparent'
                  }}
                  {...register('fullName', { required: true })}
                />
              </FormControl>

              <FormControl>
                <FormLabel color={labelColor} fontSize="sm" fontWeight="medium" mb={3}>
                  User Status
                </FormLabel>
                <Select
                  bg={bgColor}
                  isDisabled={true}
                  border="none"
                  borderRadius="lg"
                  h={12}
                  fontSize="sm"
                  color={textColor}
                  _focus={{ 
                    bg: bgColor,
                    boxShadow: 'none',
                    borderColor: 'transparent'
                  }}
                  {...register('status')}
                >
                  <option value="Approved">Active</option>
                  <option value="Pending">Inactive</option>
                </Select>
              </FormControl>
            </SimpleGrid>

            {/* Roles and Permissions Section */}
            <Box mt={8}>
              <Table variant="simple" size="lg">
                <Thead>
                  <Tr>
                    <Th 
                      border="none" 
                      bg="transparent" 
                      color={labelColor} 
                      fontSize="sm" 
                      fontWeight="medium"
                      px={4}
                      py={4}
                    >
                      Roles
                    </Th>
                    <Th 
                      border="none" 
                      bg="transparent" 
                      color={labelColor} 
                      fontSize="sm" 
                      fontWeight="medium"
                      textAlign="center"
                      py={4}
                    >
                      Dashboard
                    </Th>
                    <Th 
                      border="none" 
                      bg="transparent" 
                      color={labelColor} 
                      fontSize="sm" 
                      fontWeight="medium"
                      textAlign="center"
                      py={4}
                    >
                      Users
                    </Th>
                    <Th 
                      border="none" 
                      bg="transparent" 
                      color={labelColor} 
                      fontSize="sm" 
                      fontWeight="medium"
                      textAlign="center"
                      py={4}
                    >
                      Cards
                    </Th>
                    <Th 
                      border="none" 
                      bg="transparent" 
                      color={labelColor} 
                      fontSize="sm" 
                      fontWeight="medium"
                      textAlign="center"
                      py={4}
                    >
                      Rule
                    </Th>
                    <Th 
                      border="none" 
                      bg="transparent" 
                      color={labelColor} 
                      fontSize="sm" 
                      fontWeight="medium"
                      textAlign="center"
                      py={4}
                    >
                      Applications
                    </Th>
                  </Tr>
                </Thead>
                <Tbody>
                  <Tr>
                    <Td 
                      border="none" 
                      color={textColor} 
                      fontWeight="medium" 
                      fontSize="sm"
                      px={0}
                      py={6}
                    >
                      View
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.dashboard.view}
                        onChange={(e) => handleRoleChange('dashboard', 'view', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.users.view}
                        onChange={(e) => handleRoleChange('users', 'view', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.cards.view}
                        onChange={(e) => handleRoleChange('cards', 'view', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.rule.view}
                        onChange={(e) => handleRoleChange('rule', 'view', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.applications.view}
                        onChange={(e) => handleRoleChange('applications', 'view', e.target.checked)}
                      />
                    </Td>
                  </Tr>
                  <Tr>
                    <Td 
                      border="none" 
                      color={textColor} 
                      fontWeight="medium" 
                      fontSize="sm"
                      px={0}
                      py={6}
                    >
                      Edit
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.dashboard.edit}
                        onChange={(e) => handleRoleChange('dashboard', 'edit', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.users.edit}
                        onChange={(e) => handleRoleChange('users', 'edit', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.cards.edit}
                        onChange={(e) => handleRoleChange('cards', 'edit', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.rule.edit}
                        onChange={(e) => handleRoleChange('rule', 'edit', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.applications.edit}
                        onChange={(e) => handleRoleChange('applications', 'edit', e.target.checked)}
                      />
                    </Td>
                  </Tr>
                </Tbody>
              </Table>
            </Box>

            {/* Save Button - Centered at bottom */}
            <Box mt={16} display="flex" justifyContent="center">
              <Button
                type="submit"
                colorScheme="blue"
                size="lg"
                px={12}
                py={6}
                borderRadius="xl"
                fontWeight="medium"
                fontSize="md"
                isLoading={isLoading}
                loadingText="Saving..."
                _hover={{
                  transform: 'translateY(-1px)',
                  boxShadow: 'lg'
                }}
              >
                Save
              </Button>
            </Box>
          </VStack>
        </form>
      </Box>
    </Container>
  );
};

export default EditUser;