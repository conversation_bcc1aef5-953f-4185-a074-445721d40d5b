// src/routes/users/create-user.tsx
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormLabel,
  Heading,
  Input,
  SimpleGrid,
  VStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  useToast,
  Container,
  useColorModeValue,
  Text
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useUserStore } from '../../store/userStore';
import type { User } from '../../store/userStore';

const CreateUser = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const { createUser, isLoading } = useUserStore();
  const { register, handleSubmit, formState: { errors } } = useForm();

  // Color mode values
  const bgColor = useColorModeValue('white', 'gray.700');
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.900', 'white');
  const labelColor = useColorModeValue('gray.700', 'gray.300');

  // State for role permissions - matching the mockup structure
  const [roles, setRoles] = useState({
    dashboard: { view: false, edit: false },
    users: { view: false, edit: false },
    cards: { view: false, edit: false },
    rule: { view: false, edit: false },
    applications: { view: false, edit: false },
  });

  const handleRoleChange = (module: string, permission: string, checked: boolean) => {
    setRoles(prev => ({
      ...prev,
      [module]: {
        ...(prev[module as keyof typeof prev]),
        [permission]: checked
      }
    }));
  };

  const onSubmit = async (data: any) => {
    try {
      // Combine form data with roles
      const userData: Omit<User, 'id'> = {
        employeeCode: data.employeeCode,
        name: data.fullName, // Using fullName as name too for simplicity
        fullName: data.fullName,
        mobile: data.mobile,
        status: 'Pending', // Default status for new users
        roles: roles,
      };

      await createUser(userData);

      toast({
        title: 'User created.',
        description: 'New user has been successfully created.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      navigate('/users');
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create user. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  return (
    <Container maxW="100%" px={0}>
      <Box
        bg={cardBg}
        borderRadius="2xl"
        p={8}
        boxShadow="sm"
        minH="calc(100vh - 170px)"
      >
        <Heading size="lg" mb={8} color={textColor}>
          Create User
        </Heading>

        <form onSubmit={handleSubmit(onSubmit)}>
          <VStack spacing={8} align="stretch">
            {/* Basic Information - Two Column Layout */}
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
              <FormControl isRequired isInvalid={!!errors.employeeCode}>
                <FormLabel color={labelColor} fontSize="sm" fontWeight="medium" mb={3}>
                  Employee Code
                </FormLabel>
                <Input
                  bg={bgColor}
                  border="1px solid"
                  borderColor="gray.200"
                  borderRadius="lg"
                  h={12}
                  fontSize="sm"
                  color={textColor}
                  placeholder="Enter Employee Code"
                  _placeholder={{ color: 'gray.400' }}
                  _focus={{ 
                    bg: bgColor,
                    boxShadow: 'none',
                    // borderColor: 'gray.200'
                  }}
                  {...register('employeeCode', { required: true })}
                />
              </FormControl>

              <FormControl isRequired isInvalid={!!errors.mobile}>
                <FormLabel color={labelColor} fontSize="sm" fontWeight="medium" mb={3}>
                  Mobile
                </FormLabel>
                <Input
                  bg={bgColor}
                  border="1px solid"
                  borderColor="gray.200"
                  borderRadius="lg"
                  h={12}
                  fontSize="sm"
                  color={textColor}
                  placeholder="Enter Mobile Number"
                  _placeholder={{ color: 'gray.400' }}
                  _focus={{ 
                    bg: bgColor,
                    boxShadow: 'none',
                    // borderColor: 'gray.200'
                  }}
                  {...register('mobile', {
                    required: true,
                    pattern: {
                      value: /^\d{10}$/,
                      message: 'Please enter a valid 10-digit mobile number'
                    }
                  })}
                />
              </FormControl>
            </SimpleGrid>

            {/* Second Row - Full Name only (no status field for create) */}
            <FormControl isRequired isInvalid={!!errors.fullName}>
              <FormLabel color={labelColor} fontSize="sm" fontWeight="medium" mb={3}>
                Full Name
              </FormLabel>
              <Input
                bg={bgColor}
                border="1px solid"
                borderColor="gray.200"
                borderRadius="lg"
                h={12}
                fontSize="sm"
                color={textColor}
                placeholder="Enter Full Name"
                _placeholder={{ color: 'gray.400' }}
                _focus={{ 
                  bg: bgColor,
                  boxShadow: 'none',
                  // borderColor: 'gray.200'
                }}
                {...register('fullName', { required: true })}
              />
            </FormControl>

            {/* Roles and Permissions Section */}
            <Box mt={8}>
              <Table variant="simple" size="lg">
                <Thead>
                  <Tr>
                    <Th 
                      border="none" 
                      bg="transparent" 
                      color={labelColor} 
                      fontSize="sm" 
                      fontWeight="medium"
                      px={4}
                      py={4}
                    >
                      Roles
                    </Th>
                    <Th 
                      border="none" 
                      bg="transparent" 
                      color={labelColor} 
                      fontSize="sm" 
                      fontWeight="medium"
                      textAlign="center"
                      py={4}
                    >
                      Dashboard
                    </Th>
                    <Th 
                      border="none" 
                      bg="transparent" 
                      color={labelColor} 
                      fontSize="sm" 
                      fontWeight="medium"
                      textAlign="center"
                      py={4}
                    >
                      Users
                    </Th>
                    <Th 
                      border="none" 
                      bg="transparent" 
                      color={labelColor} 
                      fontSize="sm" 
                      fontWeight="medium"
                      textAlign="center"
                      py={4}
                    >
                      Cards
                    </Th>
                    <Th 
                      border="none" 
                      bg="transparent" 
                      color={labelColor} 
                      fontSize="sm" 
                      fontWeight="medium"
                      textAlign="center"
                      py={4}
                    >
                      Rule
                    </Th>
                    <Th 
                      border="none" 
                      bg="transparent" 
                      color={labelColor} 
                      fontSize="sm" 
                      fontWeight="medium"
                      textAlign="center"
                      py={4}
                    >
                      Applications
                    </Th>
                  </Tr>
                </Thead>
                <Tbody>
                  <Tr>
                    <Td 
                      border="none" 
                      color={textColor} 
                      fontWeight="medium" 
                      fontSize="sm"
                      px={0}
                      py={6}
                    >
                      View
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.dashboard.view}
                        onChange={(e) => handleRoleChange('dashboard', 'view', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.users.view}
                        onChange={(e) => handleRoleChange('users', 'view', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.cards.view}
                        onChange={(e) => handleRoleChange('cards', 'view', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.rule.view}
                        onChange={(e) => handleRoleChange('rule', 'view', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.applications.view}
                        onChange={(e) => handleRoleChange('applications', 'view', e.target.checked)}
                      />
                    </Td>
                  </Tr>
                  <Tr>
                    <Td 
                      border="none" 
                      color={textColor} 
                      fontWeight="medium" 
                      fontSize="sm"
                      px={0}
                      py={6}
                    >
                      Edit
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.dashboard.edit}
                        onChange={(e) => handleRoleChange('dashboard', 'edit', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.users.edit}
                        onChange={(e) => handleRoleChange('users', 'edit', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.cards.edit}
                        onChange={(e) => handleRoleChange('cards', 'edit', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.rule.edit}
                        onChange={(e) => handleRoleChange('rule', 'edit', e.target.checked)}
                      />
                    </Td>
                    <Td border="none" textAlign="center" py={6}>
                      <Checkbox
                        size="lg"
                        colorScheme="green"
                        isChecked={roles.applications.edit}
                        onChange={(e) => handleRoleChange('applications', 'edit', e.target.checked)}
                      />
                    </Td>
                  </Tr>
                </Tbody>
              </Table>
            </Box>

            {/* Create User Button - Centered at bottom */}
            <Box mt={16} display="flex" justifyContent="center">
              <Button
                type="submit"
                colorScheme="blue"
                size="lg"
                px={12}
                py={6}
                borderRadius="xl"
                fontWeight="medium"
                fontSize="md"
                isLoading={isLoading}
                loadingText="Creating..."
                _hover={{
                  transform: 'translateY(-1px)',
                  boxShadow: 'lg'
                }}
              >
                Create User
              </Button>
            </Box>
          </VStack>
        </form>
      </Box>
    </Container>
  );
};

export default CreateUser;