// src/routes/users/view-users.tsx
import { useState, useEffect } from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Heading,
  Flex,
  Text,
  Badge,
  Spinner,
  useToast,
  useDisclosure,
  useColorModeValue
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { useUserStore } from '../../store/userStore';
import { useAuthStore } from '../../store/authStore';

const ViewUsers = () => {
  const { users, isLoading, error, fetchUsers } = useUserStore();
  const navigate = useNavigate();
  const toast = useToast();
  const { user } = useAuthStore();

  const canEditUsers = user?.permissions?.find(p => p.module_name === 'users')?.can_edit;

  const tableBg = useColorModeValue('white', 'gray.800');

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  useEffect(() => {
    if (error) {
      toast({
        title: 'Error fetching users',
        description: error,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  }, [error, toast]);

  const handleViewUser = (employeeCode: string) => {
    navigate(`/users/edit/${employeeCode}`);
  };

  const handleCreateUser = () => {
    navigate('/users/create');
  };

  const getStatusText = (status: string) => {
  let color = 'gray.500';

  switch (status.toLowerCase()) {
    case 'approved':
      color = 'green.500';
      break;
    case 'pending':
      color = 'red.500';
      break;
    default:
      color = 'gray.500';
  }

  return <Text color={color} fontWeight="medium">{status}</Text>;
};

  return (
    <Box height="100%" display="flex" flexDirection="column">
      <Box
        bg={tableBg}
        borderRadius="2xl"
        p={6}
        boxShadow="md"
        height="100%"
        display="flex"
        flexDirection="column"
      >
        <Flex justifyContent="flex-end" mb={6}>
        {canEditUsers && (
        <Button colorScheme="blue" onClick={handleCreateUser}>
          Create
        </Button>
         )}
      </Flex>

      {isLoading ? (
        <Flex justifyContent="center" py={10}>
          <Spinner size="xl" />
        </Flex>
      ) : (
        <Box
          flex="1"
          display="flex"
          flexDirection="column"
          minHeight="0"
          overflow="hidden"
        >
          {/* Fixed Header */}
          <Box
            overflowX="auto"
            sx={{
              '&::-webkit-scrollbar': {
                height: '6px',
                borderRadius: '8px',
                backgroundColor: 'rgba(0, 0, 0, 0.05)',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
                borderRadius: '8px',
              },
            }}
          >
            <Table variant="unstyled" size="md">
              <Thead>
                <Tr>
                  <Th minW="140px">Employee Code</Th>
                  <Th minW="140px">Name</Th>
                  <Th minW="140px">Mobile</Th>
                  <Th minW="120px">Status</Th>
                  <Th minW="120px">Action</Th>
                </Tr>
              </Thead>
            </Table>
          </Box>

          {/* Scrollable Body */}
          <Box
            flex="1"
            overflowY="auto"
            overflowX="auto"
            sx={{
              '&::-webkit-scrollbar': {
                width: '6px',
                height: '6px',
                borderRadius: '8px',
                backgroundColor: 'rgba(0, 0, 0, 0.05)',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
                borderRadius: '8px',
              },
            }}
          >
            <Table variant="unstyled" size="md">
              <Tbody>
                {users && users.length > 0 ? (
                  users.map((user) => (
                    <Tr key={user.id}>
                      <Td minW="140px">{user.employeeCode}</Td>
                      <Td minW="140px">{user.name}</Td>
                      <Td minW="140px">{user.mobile}</Td>
                      <Td minW="120px">{getStatusText(user.status)}</Td>
                      <Td minW="120px">
                        <Button
                          size="sm"
                          variant="outline"
                          borderRadius="md"
                          px={4}
                          fontWeight="medium"
                          onClick={() => handleViewUser(user.employeeCode)}
                        >
                          View
                        </Button>
                      </Td>
                    </Tr>
                  ))
                ) : (
                  <Tr>
                    <Td colSpan={5}>
                      <Text textAlign="center">No users found</Text>
                    </Td>
                  </Tr>
                )}
              </Tbody>
            </Table>
          </Box>
        </Box>
      )}
      </Box>

    </Box>
  );
};

export default ViewUsers;