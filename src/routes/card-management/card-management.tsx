// src/routes/card-management/card-management.tsx
import { useEffect, useCallback } from 'react';
import {
  Box,
  SimpleGrid,
  Card,
  CardBody,
  Image,
  Stack,
  Text,
  CardFooter,
  Button,
  Badge,
  Flex,
  Spinner,
  Heading,
  useColorModeValue,
  Container
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { FiPlus, FiCreditCard } from 'react-icons/fi';
import { useCardStore } from '../../store/cardStore';
import { useAuthStore } from '../../store/authStore';
import { usePagination } from '../../hooks/usePagination';
import { PaginationToolbar } from '../../components/common/PaginationToolbar';

const CardManagement = () => {
  const { cards, totalCount, isLoading, error, fetchCards } = useCardStore();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const canEditUsers = user?.permissions?.find(p => p.module_name === 'cards')?.can_edit;

  const initialPageSize = 3; // Show only 3 cards per page

  const pagination = usePagination({
    initialPage: 1,
    initialPageSize,
    onPageChange: useCallback(
      (page: number, pageSize: number) => {
        fetchCards({ page, limit: pageSize });
      },
      [fetchCards]
    )
  });

  useEffect(() => {
    fetchCards({ page: 1, limit: initialPageSize });
  }, []);

  useEffect(() => {
    pagination.setTotalItems(totalCount);
  }, [totalCount]);

  const handleEdit = (id: string) => {
    navigate(`/cards/edit/${id}`);
  };

  const handleCreateCard = () => {
    navigate('/cards/create');
  };

  const cardBg = useColorModeValue('white', 'gray.700');
  const hoverBg = useColorModeValue('gray.50', 'gray.600');
  const containerBg = useColorModeValue('white', 'gray.800');

  if (isLoading && cards.length === 0) {
    return (
      <Box
        bg={containerBg}
        borderRadius="2xl"
        boxShadow="md"
        p={8}
        height="100%"
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <Spinner size="xl" color="brand.500" />
      </Box>
    );
  }

  if (error) {
    return (
      <Box
        bg={containerBg}
        borderRadius="2xl"
        boxShadow="md"
        p={8}
      >
        <Heading mb={6}>Card Management</Heading>
        <Card bg="red.50" borderRadius="lg">
          <CardBody>
            <Text color="red.500">Error loading cards: {error}</Text>
          </CardBody>
        </Card>
      </Box>
    );
  }

  return (
    <Box
      bg={containerBg}
      borderRadius="2xl"
      boxShadow="md"
      height="100%"
      display="flex"
      flexDirection="column"
      overflow="hidden"
    >
      {/* Header with Create Button */}
      <Box p={6} borderBottom="1px" borderColor="gray.200">
        <Flex justifyContent="space-between" align="center" maxW="1200px" mx="auto">
          <Heading size="lg" color="gray.700">
            Card Management
          </Heading>
          {canEditUsers && (
            <Button
              colorScheme="blue"
              onClick={handleCreateCard}
              leftIcon={<FiPlus />}
              borderRadius="lg"
              px={6}
              fontWeight="medium"
            >
              Create
            </Button>
          )}
        </Flex>
      </Box>

      {/* Main Content Area */}
      <Box
        flex="1"
        display="flex"
        flexDirection="column"
        overflow="hidden"
      >
        {cards.length === 0 ? (
          // Empty State
          <Flex
            flex="1"
            align="center"
            justify="center"
            p={8}
          >
            <Card
              bg={hoverBg}
              borderRadius="xl"
              boxShadow="sm"
              maxW="400px"
              w="100%"
            >
              <CardBody textAlign="center" py={12}>
                <Box
                  bg="gray.100"
                  w="80px"
                  h="80px"
                  borderRadius="full"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  mx="auto"
                  mb={6}
                >
                  <FiCreditCard size={32} color="#718096" />
                </Box>
                <Heading size="lg" mb={3} color="gray.700">
                  No Cards Found
                </Heading>
                <Text color="gray.500" mb={8} fontSize="md">
                  Create your first card to get started with card management
                </Text>
                {canEditUsers && (
                  <Button
                    colorScheme="blue"
                    onClick={handleCreateCard}
                    leftIcon={<FiPlus />}
                    size="lg"
                    borderRadius="lg"
                  >
                    Create Your First Card
                  </Button>
                )}
              </CardBody>
            </Card>
          </Flex>
        ) : (
          // Cards Grid and Pagination
          <>
            {/* Cards Grid Container */}
            <Box
              flex="1"
              overflowY="auto"
              p={6}
              sx={{
                '&::-webkit-scrollbar': {
                  width: '6px',
                  borderRadius: '8px',
                  backgroundColor: 'rgba(0, 0, 0, 0.05)',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                  borderRadius: '8px',
                },
              }}
            >
              <SimpleGrid
                columns={{ base: 1, md: 2, lg: 3 }}
                spacing={{ base: 6, md: 8 }}
                maxW="1200px" // Constrain total width
                mx="auto" // Center the grid container
                justifyItems="start" // Align cards to start of their cells
              >
                {cards.map(card => (
                  <Card
                    key={card.id}
                    // maxW="360px"
                    w="100%"
                    bg={cardBg}
                    boxShadow="md"
                    borderRadius="2xl"
                    transition="all 0.3s ease"
                    _hover={{
                      transform: 'translateY(-4px)',
                      boxShadow: 'xl',
                      borderColor: 'blue.200'
                    }}
                    overflow="hidden"
                    border="1px solid"
                    borderColor="gray.200"
                    height="fit-content"
                    mx={{ base: "auto", lg: "0" }} // Center on mobile, left-align on desktop
                  >
                    <CardBody p={6}>
                      <Stack spacing={4} align="center" textAlign="center">
                        {/* Card Title */}
                        <Heading 
                          size="md" 
                          color="gray.800"
                          minH="48px"
                          display="flex"
                          alignItems="center"
                        >
                          {card.title}
                        </Heading>

                        {/* Card Image */}
                        <Box
                          w="100%"
                          h="160px"
                          bg="gray.50"
                          borderRadius="xl"
                          display="flex"
                          alignItems="center"
                          justifyContent="center"
                          overflow="hidden"
                        >
                          <Image
                            src={card.image || '/placeholder-card.png'}
                            alt={card.title}
                            w="100%"
                            h="100%"
                            objectFit="cover"
                            fallbackSrc="https://via.placeholder.com/300x160?text=Card+Image"
                          />
                        </Box>

                        {/* Card Subtitle */}
                        <Text 
                          fontWeight="semibold" 
                          fontSize="sm" 
                          color="blue.600"
                          bg="blue.50"
                          px={3}
                          py={1}
                          borderRadius="full"
                        >
                          No Joining Fee. No Annual Fee
                        </Text>

                        {/* Card Features */}
                        <Stack spacing={2} fontSize="sm" color="gray.600" w="100%">
                          <Flex align="center" gap={2}>
                            <Box w={1.5} h={1.5} bg="blue.500" borderRadius="full" flexShrink={0} />
                            <Text>4 free domestic lounge visit per year</Text>
                          </Flex>
                          <Flex align="center" gap={2}>
                            <Box w={1.5} h={1.5} bg="blue.500" borderRadius="full" flexShrink={0} />
                            <Text>2% cashback on partner brands</Text>
                          </Flex>
                          <Flex align="center" gap={2}>
                            <Box w={1.5} h={1.5} bg="blue.500" borderRadius="full" flexShrink={0} />
                            <Text>1% back on spends at utilities</Text>
                          </Flex>
                          <Flex align="center" gap={2}>
                            <Box w={1.5} h={1.5} bg="blue.500" borderRadius="full" flexShrink={0} />
                            <Text>1st year free: $0 save on fee</Text>
                          </Flex>
                        </Stack>
                      </Stack>
                    </CardBody>

                    {/* Card Footer */}
                    <CardFooter 
                      pt={0} 
                      pb={6} 
                      px={6}
                      sx={{
                        '&::before': {
                          display: 'none !important',
                        }
                      }}
                    >
                      <Flex 
                        justify="space-between" 
                        align="center" 
                        w="100%"
                        gap={3}
                      >
                        <Badge
                          colorScheme={
                            card.status === 'Active'
                              ? 'green'
                              : card.status === 'inactive'
                                ? 'gray'
                                : 'gray'
                          }
                          variant={card.status === 'Active' ? 'subtle' : 'solid'}
                          px={4}
                          py={2}
                          borderRadius="full"
                          fontSize="sm"
                          fontWeight="medium"
                          textTransform="none"
                          bg={card.status === 'Active' ? 'green.100' : 'gray.500'}
                          color={card.status === 'Active' ? 'green.700' : 'white'}
                        >
                          {card.status === 'Active'
                            ? 'Active'
                            : card.status === 'inactive'
                              ? 'Inactive'
                              : 'Unknown'}
                        </Badge>

                        {canEditUsers && (
                          <Button
                            variant="solid"
                            colorScheme="blue"
                            size="md"
                            onClick={() => handleEdit(card.id)}
                            borderRadius="lg"
                            px={4}
                            py={2}
                            fontWeight="medium"
                            fontSize="sm"
                            bg="brand.500"
                            color="white"
                            _hover={{
                              bg: "blue.700",
                              transform: 'translateY(-1px)',
                            }}
                            _active={{
                              bg: "blue.800",
                            }}
                          >
                            Edit Details
                          </Button>
                        )}
                      </Flex>
                    </CardFooter>
                  </Card>
                ))}
              </SimpleGrid>
            </Box>

            {/* Pagination Toolbar */}
            {totalCount > 0 && (
              <PaginationToolbar
                pagination={pagination}
                isLoading={isLoading}
                pageSizeOptions={[3, 6, 9, 12]}
                showPageSizeSelector={true}
              />
            )}
          </>
        )}
      </Box>
    </Box>
  );
};

export default CardManagement;