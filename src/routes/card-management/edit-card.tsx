// src/routes/cards/edit-card.tsx
import {
  Box,
  Button,
  Container,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Input,
  Textarea,
  Select,
  Heading,
  VStack,
  Grid,
  GridItem,
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Image,
  Text,
  useToast,
  HStack,
  Icon,
  useColorModeValue,
  Badge,
  Spinner,
  Center,
   InputGroup,
  InputLeftElement,
  InputRightElement,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { FiUpload } from 'react-icons/fi';
import { useCardStore } from '../../store/cardStore';
import { useRuleStore } from '../../store/ruleStore';
import { UpdateCardData } from '../../types/card';
import { useState, useEffect } from 'react';

interface EditCardFormData {
  name: string;
  subHeader: string;
  description: string;
  imageUrl: string;
  utmLink: string;
  ruleId: string;
  earning: string;
  status: 'active' | 'inactive' | 'pending';
}

const EditCard = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const toast = useToast();
  const { selectedCard, updateCard, isLoading: isUpdating, fetchCard } = useCardStore();
  const { rules, fetchRules, isLoading: isLoadingRules } = useRuleStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingCard, setIsLoadingCard] = useState(true);
  const [selectedImage, setSelectedImage] = useState<string>('');
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<EditCardFormData>();

  const watchedFields = watch();

  useEffect(() => {
    const loadData = async () => {
      setIsLoadingCard(true);

      if (id) {
        try {
          await Promise.all([fetchCard(id), fetchRules()]);

          if (selectedCard) {
            reset({
              name: selectedCard.title,
              subHeader: selectedCard.title, // Using title as subHeader as it's not available
              description: selectedCard.description,
              imageUrl: selectedCard.image,
              utmLink: '', // Not available in the Card type
              ruleId: selectedCard.templateId, // Using templateId as ruleId
              earning: '', // Not available in the Card type
              status: selectedCard.status,
            });
            setSelectedImage(selectedCard.image);
          } else {
            toast({
              title: 'Card not found',
              status: 'error',
              duration: 3000,
              isClosable: true,
            });
            navigate('/cards');
          }
        } catch (error) {
          toast({
            title: 'Error loading card',
            description: error instanceof Error ? error.message : 'Unknown error',
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
          navigate('/cards');
        }
      }

      setIsLoadingCard(false);
    };

    loadData();
  }, [id, fetchCard, fetchRules, reset, navigate, toast, selectedCard]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        setSelectedImage(base64String);
        setValue('imageUrl', base64String);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: EditCardFormData) => {
    if (!id) return;

    setIsSubmitting(true);
    try {
      const updateData: UpdateCardData = {
        name: data.name,
        subHeader: data.subHeader,
        description: data.description,
        imageUrl: data.imageUrl,
        utmLink: data.utmLink,
        ruleId: data.ruleId,
        earning: data.earning,
        status: data.status,
        benefits: data.description.split('\n').filter(line => line.trim().startsWith('•')).map(line => line.trim()),
      };

      await updateCard(id, updateData);
      toast({
        title: 'Card updated successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      navigate('/cards');
    } catch (error) {
      toast({
        title: 'Failed to update card',
        description: error instanceof Error ? error.message : 'Unknown error',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoadingCard) {
    return (
      <Center minH="400px">
        <Spinner size="xl" />
      </Center>
    );
  }

  return (
    <Box 
      bg="white"
      p={6}
      borderRadius="2xl"
      boxShadow="md"
      maxW="7xl"
      mx="auto">
    <Container maxW="container.xl" py={6}>
      <VStack spacing={6} align="stretch">

        <Grid templateColumns={{ base: '1fr', lg: '1fr 1fr' }} gap={8}>
          <GridItem>
            <form onSubmit={handleSubmit(onSubmit)}>
              <VStack spacing={4} align="stretch">
                <FormControl isInvalid={!!errors.name} isRequired>
                  <FormLabel>Card Name</FormLabel>
                  <Input
                    {...register('name', { required: 'Card name is required' })}
                    placeholder="Tata Neu Plus HDFC Bank Credit Card"
                    bg={cardBg}
                  />
                  <FormErrorMessage>{errors.name?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.subHeader} isRequired>
                  <FormLabel>Sub Header</FormLabel>
                  <Input
                    {...register('subHeader', { required: 'Sub header is required' })}
                    placeholder="No Joining Fee. No Annual Fee"
                    bg={cardBg}
                  />
                  <FormErrorMessage>{errors.subHeader?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.description} isRequired>
                  <FormLabel>Description</FormLabel>
                  <Textarea
                    {...register('description', { required: 'Description is required' })}
                    placeholder="• 4 free domestic lounge visit per year
• 2% cashback on partner brands
• 1% back on spends at utilities
• 1st year free: $0 save on fee"
                    rows={4}
                    bg={cardBg}
                  />
                  <FormErrorMessage>{errors.description?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.imageUrl}>
                  <FormLabel>Upload Card Image</FormLabel>
                  <InputGroup
                    border="1px"
                    borderColor="gray.200"
                    height={45}
                    borderRadius="md"
                    overflow="hidden"
                    _hover={{ borderColor: "gray.300" }}
                  >
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                      opacity={0}
                      zIndex={2}
                      position="absolute"
                      right={0}
                      top={0}
                      h="100%"
                      cursor="pointer"
                      id="card-image"
                    />
                    <InputRightElement pr={3} pointerEvents="none">
                      <Icon as={FiUpload} color="blue.500" />
                    </InputRightElement>
                    {selectedImage && <Text fontSize="sm">Image selected</Text>}

                    {selectedImage && <Text fontSize="sm">Image selected</Text>}
                  </InputGroup>
                  <FormErrorMessage>{errors.imageUrl?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.utmLink} isRequired>
                  <FormLabel>UTM Link</FormLabel>
                  <Input
                    {...register('utmLink', { required: 'UTM link is required' })}
                    placeholder="Enter UTM Link"
                    bg={cardBg}
                  />
                  <FormErrorMessage>{errors.utmLink?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.ruleId} isRequired>
                  <FormLabel>Attach Rule</FormLabel>
                  <Select
                    {...register('ruleId', { required: 'Please select a rule' })}
                    placeholder="Select Rule"
                    bg={cardBg}
                  >
                    {isLoadingRules ? (
                      <option>Loading rules...</option>
                    ) : (
                      rules.map((rule: { id: string; name: string }) => (
                        <option key={rule.id} value={rule.id}>
                          {rule.name}
                        </option>
                      ))
                    )}
                  </Select>
                  <FormErrorMessage>{errors.ruleId?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.earning} isRequired>
                  <FormLabel>Earning</FormLabel>
                  <Input
                    {...register('earning', { required: 'Earning is required' })}
                    placeholder="Enter Earning"
                    bg={cardBg}
                  />
                  <FormErrorMessage>{errors.earning?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.status} isRequired>
                  <FormLabel>Status</FormLabel>
                  <Select
                    {...register('status', { required: 'Please select a status' })}
                    bg={cardBg}
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="pending">Pending</option>
                  </Select>
                  <FormErrorMessage>{errors.status?.message}</FormErrorMessage>
                </FormControl>

                <HStack spacing={4} pt={4}>
                  <Button
                    flex={1}
                    colorScheme="blue"
                    type="submit"
                    isLoading={isSubmitting || isUpdating}
                    loadingText="Updating..."
                  >
                    Save Changes
                  </Button>
                </HStack>
              </VStack>
            </form>
          </GridItem>

          <GridItem>
            <VStack align="stretch" spacing={4}>
              <Card
                bg={cardBg}
                overflow="hidden"
              >
                <CardHeader pb={2}>
                  <VStack align="start" spacing={1}>
                    <HStack justify="space-between" w="full">
                      <Text fontSize="lg" fontWeight="bold">
                        {watchedFields.name || 'TATA Neu Plus HDFC Bank Credit Card'}
                      </Text>
                      <Badge colorScheme={watchedFields.status === 'active' ? 'green' : watchedFields.status === 'pending' ? 'yellow' : 'gray'} variant="subtle">
                        {watchedFields.status?.charAt(0).toUpperCase() + watchedFields.status?.slice(1) || 'Active'}
                      </Badge>
                    </HStack>
                  </VStack>
                </CardHeader>

                <CardBody py={4}>
                  <Box
                    position="relative"
                    minH="160px"
                    overflow="hidden"
                    borderRadius="md"
                    bg="#2A1A4A"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    {selectedImage ? (
                      <Image
                        src={selectedImage}
                        alt="Card preview"
                        maxH="160px"
                        objectFit="contain"
                      />
                    ) : (
                      <Text color="white" fontSize="sm">Upload card image to preview</Text>
                    )}
                  </Box>
                  <VStack align="start" mt={4} spacing={1}>
                    <Text fontSize="sm" fontWeight="semibold">
                      {watchedFields.subHeader || 'No Joining Fee. No Annual Fee'}
                    </Text>
                    <VStack align="start" spacing={0.5} fontSize="xs" color="gray.600">
                      {watchedFields.description?.split('\n').map((line, index) => (
                        line.trim() && <Text key={index}>{line}</Text>
                      ))}
                    </VStack>
                  </VStack>
                </CardBody>

                <CardFooter pt={2}>
                  <Button size="sm" colorScheme="blue" w="full">
                    Apply Now
                  </Button>
                </CardFooter>
              </Card>
            </VStack>
          </GridItem>
        </Grid>
      </VStack>
    </Container>
    </Box>
  );
};

export default EditCard;