// src/routes/cards/create-card.tsx
import {
  Box,
  Button,
  Container,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Input,
  Textarea,
  Select,
  Heading,
  VStack,
  Grid,
  Flex,
  GridItem,
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Image,
  Text,
  useToast,
  HStack,
  Icon,
  useColorModeValue,
  Badge,
  Spinner,
  InputGroup,
  InputLeftElement,
  InputRightElement,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { FiUpload } from 'react-icons/fi';
import { useCardStore } from '../../store/cardStore';
import { useRuleStore } from '../../store/ruleStore';
import { CreateCardData } from '../../types/card';
import { useState, useEffect } from 'react';

interface CreateCardFormData {
  name: string;
  subHeader: string;
  description: string;
  imageUrl: string;
  utmLink: string;
  ruleId: string;
  earning: string;
  status: 'active' | 'inactive' | 'pending';
}

const CreateCard = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const { createCard, isLoading: isCreating } = useCardStore();
  const { rules, fetchRules, isLoading: isLoadingRules } = useRuleStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string>('');
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CreateCardFormData>({
    defaultValues: {
      name: '',
      subHeader: '',
      description: '',
      imageUrl: '',
      utmLink: '',
      ruleId: '',
      earning: '',
      status: 'active',
    },
  });

  const watchedFields = watch();

  useEffect(() => {
    fetchRules();
  }, [fetchRules]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedImage(URL.createObjectURL(file)); // for preview
      setValue('imageUrl', file); // store the actual File object
    }
  };

  const onSubmit = async (data: CreateCardFormData) => {
    setIsSubmitting(true);
    try {
      const createData: CreateCardData = {
        name: data.name,
        subHeader: data.subHeader,
        description: data.description,
        imageUrl: data.imageUrl || '/placeholder-card.png',
        utmLink: data.utmLink,
        ruleId: data.ruleId,
        earning: data.earning,
        status: data.status,
        benefits: data.description.split('\n').filter(line => line.trim().startsWith('•')).map(line => line.trim()),
      };

      await createCard(createData);
      toast({
        title: 'Card created successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      navigate('/cards');
    } catch (error) {
      toast({
        title: 'Failed to create card',
        description: error instanceof Error ? error.message : 'Unknown error',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
     <Box 
      bg="white"
      p={6}
      borderRadius="2xl"
      boxShadow="md"
      maxW="7xl"
      mx="auto">
    <Container maxW="container.xl" py={6}>
      <VStack spacing={6} align="stretch">

        <Grid templateColumns={{ base: '1fr', lg: '1fr 1fr' }} gap={8}>
          <GridItem>
            <form onSubmit={handleSubmit(onSubmit)}>
              <VStack spacing={4} align="stretch">
                <FormControl isInvalid={!!errors.name} isRequired>
                  <FormLabel>Card Name</FormLabel>
                  <Input
                    {...register('name', { required: 'Card name is required' })}
                    placeholder="Tata Neu Plus HDFC Bank Credit Card"
                    bg={cardBg}
                  />
                  <FormErrorMessage>{errors.name?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.subHeader} isRequired>
                  <FormLabel>Sub Header</FormLabel>
                  <Input
                    {...register('subHeader', { required: 'Sub header is required' })}
                    placeholder="No Joining Fee. No Annual Fee"
                    bg={cardBg}
                  />
                  <FormErrorMessage>{errors.subHeader?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.description} isRequired>
                  <FormLabel>Description</FormLabel>
                  <Textarea
                    {...register('description', { required: 'Description is required' })}
                    placeholder="• 4 free domestic lounge visit per year
    • 2% cashback on partner brands
    • 1% back on spends at utilities
    • 1st year free: $0 save on fee"
                    rows={4}
                    bg={cardBg}
                  />
                  <FormErrorMessage>{errors.description?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.imageUrl}>
                  <FormLabel>Upload Card Image</FormLabel>
                  <InputGroup
                    border="1px"
                    borderColor="gray.200"
                    height={45}
                    borderRadius="md"
                    overflow="hidden"
                    _hover={{ borderColor: "gray.300" }}
                  >
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                      opacity={0}
                      zIndex={2}
                      position="absolute"
                      right={0}
                      top={0}
                      h="100%"
                      cursor="pointer"
                      id="card-image"
                    />
                    <InputRightElement pr={3} pointerEvents="none">
                      <Icon as={FiUpload} color="blue.500" />
                    </InputRightElement>
                    {selectedImage && <Text fontSize="sm">Image selected</Text>}
                  </InputGroup>
                  <FormErrorMessage>{errors.imageUrl?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.utmLink} isRequired>
                  <FormLabel>UTM Link</FormLabel>
                  <Input
                    {...register('utmLink', { required: 'UTM link is required' })}
                    placeholder="Enter UTM Link"
                    bg={cardBg}
                  />
                  <FormErrorMessage>{errors.utmLink?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.ruleId} isRequired>
                  <FormLabel>Attach Rule</FormLabel>
                  <Select
                    {...register('ruleId', { required: 'Please select a rule' })}
                    placeholder="Select Rule"
                    bg={cardBg}
                  >
                    {isLoadingRules ? (
                      <option>Loading rules...</option>
                    ) : (
                      rules.map((rule) => (
                        <option key={rule.id} value={rule.id}>
                          {rule.name}
                        </option>
                      ))
                    )}
                  </Select>
                  <FormErrorMessage>{errors.ruleId?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.earning} isRequired>
                  <FormLabel>Earning</FormLabel>
                  <Input
                    {...register('earning', { required: 'Earning is required' })}
                    placeholder="Enter Earning"
                    bg={cardBg}
                  />
                  <FormErrorMessage>{errors.earning?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.status} isRequired>
                  <FormLabel>Status</FormLabel>
                  <Select
                    {...register('status', { required: 'Please select a status' })}
                    bg={cardBg}
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="pending">Pending</option>
                  </Select>
                  <FormErrorMessage>{errors.status?.message}</FormErrorMessage>
                </FormControl>

                <HStack spacing={4} pt={4}>
                  <Button
                    flex={1}
                    colorScheme="blue"
                    type="submit"
                    isLoading={isSubmitting || isCreating}
                    loadingText="Creating..."
                  >
                    Create
                  </Button>
                </HStack>
              </VStack>
            </form>
          </GridItem>

          <GridItem>
            <VStack align="stretch" spacing={4}>
              <Card
                bg={cardBg}
                overflow="hidden"
              >
                <CardHeader pb={2}>
                  <VStack align="start" spacing={1}>
                    <HStack justify="space-between" w="full">
                      <Text fontSize="lg" fontWeight="bold">
                        {watchedFields.name || 'TATA Neu Plus HDFC Bank Credit Card'}
                      </Text>
                      <Badge colorScheme="green" variant="subtle">
                        {watchedFields.status?.charAt(0).toUpperCase() + watchedFields.status?.slice(1) || 'Active'}
                      </Badge>
                    </HStack>
                  </VStack>
                </CardHeader>

                <CardBody py={4}>
                  <Box
                    position="relative"
                    minH="160px"
                    overflow="hidden"
                    borderRadius="md"
                    bg="#2A1A4A"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    {selectedImage ? (
                      <Image
                        src={selectedImage}
                        alt="Card preview"
                        maxH="160px"
                        objectFit="contain"
                      />
                    ) : (
                      <Text color="white" fontSize="sm">Upload card image to preview</Text>
                    )}
                  </Box>
                  <VStack align="start" mt={4} spacing={1}>
                    <Text fontSize="sm" fontWeight="semibold">
                      {watchedFields.subHeader || 'No Joining Fee. No Annual Fee'}
                    </Text>
                    <VStack align="start" spacing={0.5} fontSize="xs" color="gray.600">
                      {watchedFields.description?.split('\n').map((line, index) => (
                        line.trim() && <Text key={index}>{line}</Text>
                      ))}
                    </VStack>
                  </VStack>
                </CardBody>

                <CardFooter pt={2}>
                  <Button size="sm" colorScheme="blue" w="full">
                    Apply Now
                  </Button>
                </CardFooter>
              </Card>
            </VStack>
          </GridItem>
        </Grid>
      </VStack>
    </Container>
    </Box>
    // <Container maxW="container.lg" py={10}>
    //   <Box bg="white" p={8} rounded="xl" shadow="md">
    //     <Grid templateColumns={{ base: '1fr', md: '1fr 0.8fr' }} gap={10}>
    //       {/* Form Section */}
    //       <form onSubmit={handleSubmit(onSubmit)}>
    //         <VStack spacing={4} align="stretch">
    //           <FormControl isInvalid={!!errors.name} isRequired>
    //             <FormLabel>Card Name</FormLabel>
    //             <Input {...register('name')} placeholder="Enter Card Name" />
    //             <FormErrorMessage>{errors.name?.message}</FormErrorMessage>
    //           </FormControl>

    //           <FormControl isInvalid={!!errors.subHeader} isRequired>
    //             <FormLabel>Sub Header</FormLabel>
    //             <Input {...register('subHeader')} placeholder="Enter Sub Header" />
    //             <FormErrorMessage>{errors.subHeader?.message}</FormErrorMessage>
    //           </FormControl>

    //           <FormControl isInvalid={!!errors.description} isRequired>
    //             <FormLabel>Description</FormLabel>
    //             <Textarea
    //               {...register('description')}
    //               placeholder="Enter Description"
    //               rows={4}
    //             />
    //             <FormErrorMessage>{errors.description?.message}</FormErrorMessage>
    //           </FormControl>

    //           <FormControl isRequired>
    //             <FormLabel>Upload Card Image</FormLabel>
    //             <InputGroup
    //               border="1px"
    //               borderColor="gray.200"
    //               height={45}
    //               borderRadius="md"
    //               overflow="hidden"
    //               _hover={{ borderColor: "gray.300" }}
    //             >

    //               <Input
    //                 type="file"
    //                 opacity={0}
    //                 zIndex={2}
    //                 position="absolute"
    //                 right={0}
    //                 top={0}
    //                 h="100%"
    //                 cursor="pointer"
    //               />

    //               <InputRightElement pr={3} pointerEvents="none">
    //                 <Icon as={FiUpload} color="blue.500" />
    //               </InputRightElement>
    //             </InputGroup>
    //           </FormControl>

    //           <FormControl isInvalid={!!errors.utmLink} isRequired>
    //             <FormLabel>UTM Link</FormLabel>
    //             <Input {...register('utmLink')} placeholder="Enter UTM Link" />
    //             <FormErrorMessage>{errors.utmLink?.message}</FormErrorMessage>
    //           </FormControl>

    //           <FormControl isInvalid={!!errors.ruleId} isRequired>
    //             <FormLabel>Attach Rule</FormLabel>
    //             <Select {...register('ruleId')} placeholder="Select Rule">
    //               {rules.map(rule => (
    //                 <option key={rule.id} value={rule.id}>{rule.name}</option>
    //               ))}
    //             </Select>
    //             <FormErrorMessage>{errors.ruleId?.message}</FormErrorMessage>
    //           </FormControl>

    //           <FormControl isInvalid={!!errors.earning} isRequired>
    //             <FormLabel>Earning</FormLabel>
    //             <Input {...register('earning')} placeholder="Enter Earning" />
    //             <FormErrorMessage>{errors.earning?.message}</FormErrorMessage>
    //           </FormControl>

    //           <FormControl isInvalid={!!errors.status} isRequired>
    //             <FormLabel>Status</FormLabel>
    //             <Select {...register('status')}>
    //               <option value="active">Active</option>
    //               <option value="inactive">Inactive</option>
    //               <option value="pending">Pending</option>
    //             </Select>
    //             <FormErrorMessage>{errors.status?.message}</FormErrorMessage>
    //           </FormControl>
    //         </VStack>
    //       </form>

    //       {/* Preview Section */}
    //       <VStack spacing={4} align="stretch">
    //         <Box border="1px" borderColor="gray.200" rounded="3xl" p={4}>
    //           <Text fontWeight="bold" fontSize="md">
    //             {watchedFields.name || 'Enter Card Name'}
    //           </Text>
    //           <Box
    //             h="120px"
    //             bg="gray.200"
    //             rounded="md"
    //             mt={2}
    //             display="flex"
    //             alignItems="center"
    //             justifyContent="center"
    //           >
    //             {selectedImage ? (
    //               <Image src={selectedImage} alt="preview" objectFit="contain" maxH="100%" />
    //             ) : (
    //               <Text fontSize="sm" color="gray.500">No Image</Text>
    //             )}
    //           </Box>
    //           <Text fontWeight="semibold" mt={3}>
    //             {watchedFields.subHeader || 'Enter Sub Heading'}
    //           </Text>
    //           <Box mt={1}>
    //             {watchedFields.description?.split('\n').map((line, idx) => (
    //               <Text key={idx} fontSize="sm">• {line.replace(/^•/, '').trim()}</Text>
    //             ))}
    //           </Box>
    //           <Button
    //             mt={4}
    //             w="full"
    //             size="sm"
    //             bg="gray.400"
    //             color="white"
    //             _hover={{ bg: 'gray.500' }}
    //             isDisabled
    //           >
    //             Apply Now
    //           </Button>
    //         </Box>
    //       </VStack>
    //     </Grid>

    //     {/* Create Button */}
    //     <Flex mt={8} justify="center">
    //       <Button
    //         mt={8}
    //         w="30"
    //         colorScheme="gray"
    //         type="submit"
    //         isLoading={isSubmitting}
    //       >
    //         Create
    //       </Button>
    //     </Flex>
    //   </Box>
    // </Container>

  );
};

export default CreateCard;