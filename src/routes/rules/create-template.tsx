// src/routes/rules/create-template.tsx
import React, { useState, useRef } from 'react';
import {
  Box,
  Heading,
  FormControl,
  FormLabel,
  Input,
  Select,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Flex,
  useToast,
  useColorModeValue,
  Container,
  VStack,
} from '@chakra-ui/react';
import { FiUpload } from "react-icons/fi";
import { useNavigate } from 'react-router-dom';
import { useTemplateStore } from '../../store/templateStore';
import { useForm } from 'react-hook-form';

interface RuleField {
  name: string;
  condition: string;
  value: string;
  action: string;
}

interface TemplateForm {
  name: string;
  templateNumber: string;
  rules: RuleField[];
}

const CreateTemplate = () => {
  const navigate = useNavigate();
  const { addTemplate } = useTemplateStore();
  const toast = useToast();
  const { register, handleSubmit, formState: { errors } } = useForm<TemplateForm>();

  // Color mode values
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const inputBg = useColorModeValue('white', 'gray.700');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const [rules, setRules] = useState<RuleField[]>([
    { name: 'Age Requirement', condition: '=', value: '', action: 'Hard' },
    { name: 'Gender', condition: '=', value: 'M', action: 'Soft' },
    { name: 'Pincode', condition: '=', value: '', action: 'Hard' },
    { name: 'Credit Score Requirement', condition: '=', value: 'F', action: 'Hard' },
    { name: 'Credit History Length', condition: '=', value: '', action: 'Soft' },
  ]);

  const handleRuleChange = (index: number, field: keyof RuleField, value: string) => {
    const newRules = [...rules];
    newRules[index] = { ...newRules[index], [field]: value };
    setRules(newRules);
  };

  const onSubmit = async (data: TemplateForm) => {
    try {
      const template = {
        id: Date.now().toString(),
        name: data.templateNumber, // Using templateNumber as name
        templateNumber: data.templateNumber,
        date: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        }),
        cardLinked: '0',
        rules: rules,
      };

      await addTemplate(template);

      toast({
        title: 'Template created successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      navigate('/rules');
    } catch (error) {
      toast({
        title: 'Error creating template',
        description: 'Failed to create template. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleFileClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      console.log('Selected file:', file);
      // TODO: parse and update Pincode rule.value if needed
    }
  };

  return (
    <Container maxW="100%" p={0}>
      <Box
        bg={bgColor}
        borderRadius="xl"
        boxShadow="sm"
        border="1px"
        borderColor={borderColor}
        overflow="hidden"
      >
        {/* Header */}
        <Box p={8} pb={6}>
          <Heading size="lg" color={textColor} mb={8} fontWeight="semibold">
            Create Template
          </Heading>

          <form onSubmit={handleSubmit(onSubmit)}>
            <VStack spacing={8} align="stretch">
              {/* Template Number Input */}
              <Box maxW="400px">
                <FormControl isInvalid={!!errors.templateNumber}>
                  <FormLabel color={textColor} fontSize="sm" fontWeight="medium" mb={3}>
                    Template Number
                  </FormLabel>
                  <Input
                    placeholder="Enter Template Name"
                    bg={inputBg}
                    border="1px"
                    borderColor={borderColor}
                    borderRadius="lg"
                    fontSize="sm"
                    h="44px"
                    color={textColor}
                    _placeholder={{ color: 'gray.400' }}
                    _focus={{
                      borderColor: 'blue.500',
                      boxShadow: '0 0 0 1px blue.500'
                    }}
                    {...register('templateNumber', { required: 'Template number is required' })}
                  />
                </FormControl>
              </Box>

              {/* Rules Table */}
              <Box>
                <Box overflowX="auto">
                  <Table variant="unstyled" size="md">
                    <Thead>
                      <Tr>
                        <Th
                          fontSize="sm"
                          fontWeight="medium"
                          textTransform="none"
                          letterSpacing="normal"
                          minW="180px"
                          py={4}
                        >
                          Rule Name
                        </Th>
                        <Th
                          fontSize="sm"
                          fontWeight="medium"
                          textTransform="none"
                          letterSpacing="normal"
                          minW="120px"
                          py={4}
                        >
                          Condition
                        </Th>
                        <Th
                          fontSize="sm"
                          fontWeight="medium"
                          textTransform="none"
                          letterSpacing="normal"
                          minW="140px"
                          py={4}
                        >
                          Value
                        </Th>
                        <Th
                          fontSize="sm"
                          fontWeight="medium"
                          textTransform="none"
                          letterSpacing="normal"
                          minW="100px"
                          py={4}
                        >
                          Action
                        </Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {rules.map((rule, index) => (
                        <Tr key={index}>
                          <Td py={4} color={textColor} fontSize="sm" fontWeight="medium">
                            {rule.name}
                          </Td>
                          <Td py={4} textAlign="center">
                            <Select
                              value={rule.condition}
                              onChange={(e) => handleRuleChange(index, 'condition', e.target.value)}
                              bg="transparent"
                              border="1px"
                              borderColor="brand.400"
                              borderRadius="lg"
                              fontSize="sm"
                              h="40px"
                              color="brand.400"
                              fontWeight="medium"
                              _focus={{
                                borderColor: 'brand.600'
                              }}
                            >
                              <option value="=">=</option>
                              <option value=">">&gt;</option>
                              <option value="<">&lt;</option>
                              <option value=">=">&gt;=</option>
                              <option value="<=">&lt;=</option>
                            </Select>
                          </Td>
                          <Td py={4} textAlign="center">
                            {rule.name === 'Gender' ? (
                              <Select
                                value={rule.value}
                                onChange={(e) => handleRuleChange(index, 'value', e.target.value)}
                                bg="transparent"
                                border="1px"
                                borderColor="brand.400"
                                borderRadius="lg"
                                fontSize="sm"
                                h="40px"
                                color="blue.600"
                                fontWeight="medium"
                                _focus={{
                                  borderColor: 'brand.400',
                                  boxShadow: '0 0 0 1px blue.600'
                                }}
                              >
                                <option value="M">M</option>
                                <option value="F">F</option>
                                <option value="Other">Other</option>
                              </Select>
                            ) : rule.name === 'Credit Score Requirement' ? (
                              <Select
                                value={rule.value}
                                onChange={(e) => handleRuleChange(index, 'value', e.target.value)}
                                bg="transparent"
                                border="1px"
                                borderColor="brand.400"
                                borderRadius="lg"
                                fontSize="sm"
                                h="40px"
                                color="blue.600"
                                fontWeight="medium"
                                _focus={{
                                  borderColor: 'brand.400'
                                }}
                              >
                                <option value="A">A</option>
                                <option value="B">B</option>
                                <option value="C">C</option>
                                <option value="D">D</option>
                                <option value="F">F</option>
                              </Select>
                            ) : rule.name === 'Pincode' ? (
                              <>
                                <Input
                                  type="file"
                                  ref={fileInputRef}
                                  onChange={handleFileChange}
                                  display="none"
                                  accept=".csv,.txt" // optional
                                />
                                <Button
                                  leftIcon={<FiUpload />}
                                  variant="outline"
                                  size="sm"
                                  border="1px"
                                  borderColor="brand.400"
                                  color="blue.600"
                                  borderRadius="lg"
                                  fontSize="sm"
                                  h="40px"
                                  px={4}
                                  fontWeight="medium"
                                  _hover={{ borderColor: 'brand.400' }}
                                  onClick={handleFileClick}
                                >
                                  Upload
                                </Button>
                              </>
                            ) : (
                              <Input
                                placeholder="Enter Value"
                                value={rule.value}
                                onChange={(e) => handleRuleChange(index, 'value', e.target.value)}
                                bg="transparent"
                                border="1px"
                                borderColor="brand.400"
                                borderRadius="lg"
                                fontSize="sm"
                                h="40px"
                                color="blue.600"
                                fontWeight="medium"
                                _placeholder={{ color: 'brand.400' }}
                                _focus={{
                                  borderColor: 'brand.400'
                                }}
                              />
                            )}
                          </Td>
                          <Td py={4} textAlign="center">
                            <Select
                              value={rule.action}
                              onChange={(e) => handleRuleChange(index, 'action', e.target.value)}
                              bg="transparent"
                              border="1px"
                              borderColor="brand.400"
                              borderRadius="lg"
                              fontSize="sm"
                              h="40px"
                              color="brand.400"
                              fontWeight="medium"
                              _focus={{
                                borderColor: 'brand.400'
                              }}
                            >
                              <option value="Hard">Hard</option>
                              <option value="Soft">Soft</option>
                            </Select>
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </Box>
              </Box>

              {/* Submit Button */}
              <Flex justifyContent="center" pt={6}>
                <Button
                  type="submit"
                  colorScheme="blue"
                  bg="blue.600"
                  color="white"
                  size="lg"
                  px={12}
                  py={6}
                  fontSize="sm"
                  fontWeight="medium"
                  borderRadius="lg"
                  _hover={{ bg: 'blue.700' }}
                  _active={{ bg: 'blue.800' }}
                >
                  Create
                </Button>
              </Flex>
            </VStack>
          </form>
        </Box>
      </Box>
    </Container>
  );
};

export default CreateTemplate;