// src/routes/rules/view-templates.tsx
import React from 'react';
import {
    Box,
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    Button,
    Text,
    Card,
    CardBody,
    useColorModeValue,
} from '@chakra-ui/react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { FiPlus } from 'react-icons/fi';
import { useTemplateStore } from '../../store/templateStore';

const ViewTemplates = () => {
    const { templates, isLoading } = useTemplateStore();
    const navigate = useNavigate();
    const bgColor = useColorModeValue('white', 'gray.800');
    const borderColor = useColorModeValue('gray.100', 'gray.700');

    const handleCreateTemplate = () => {
        navigate('/rules/create');
    };

    return (
        <Box height="100%" display="flex" flexDirection="column">
            <Card
                bg={bgColor}
                boxShadow="sm"
                borderRadius="2xl"
                borderWidth="1px"
                borderColor={borderColor}
                p={5}
                height="100%"
                display="flex"
                flexDirection="column"
            >
                <CardBody p={0} display="flex" flexDirection="column" height="100%">
                    <Box display="flex" justifyContent="flex-end" mb={6}>
                    <Button
                        colorScheme="brand"
                        onClick={handleCreateTemplate}
                        leftIcon={<FiPlus />}
                    >
                        Create Template
                    </Button>
                    </Box>

                    {isLoading ? (
                        <Box display="flex" justifyContent="center" py={10}>
                            <Text>Loading templates...</Text>
                        </Box>
                    ) : templates.length === 0 ? (
                        <Box display="flex" flexDirection="column" alignItems="center" py={10}>
                            <Text fontSize="md" color="gray.500" mb={4}>
                                No templates found. Create your first template!
                            </Text>
                            <Button
                                colorScheme="brand"
                                size="sm"
                                leftIcon={<FiPlus />}
                                onClick={handleCreateTemplate}
                            >
                                Create Template
                            </Button>
                        </Box>
                    ) : (
                        <Box
                            flex="1"
                            display="flex"
                            flexDirection="column"
                            minHeight="0"
                            overflow="hidden"
                        >
                            {/* Fixed Header */}
                            <Box
                                overflowX="auto"
                                sx={{
                                    '&::-webkit-scrollbar': {
                                        height: '6px',
                                        borderRadius: '8px',
                                        backgroundColor: 'rgba(0, 0, 0, 0.05)',
                                    },
                                    '&::-webkit-scrollbar-thumb': {
                                        backgroundColor: 'rgba(0, 0, 0, 0.1)',
                                        borderRadius: '8px',
                                    },
                                }}
                            >
                                <Table variant="unstyled" size="md">
                                    <Thead>
                                        <Tr>
                                            <Th minW="120px">Date</Th>
                                            <Th minW="200px">Template Name</Th>
                                            <Th minW="150px">Card Linked</Th>
                                            <Th minW="120px">Action</Th>
                                        </Tr>
                                    </Thead>
                                </Table>
                            </Box>

                            {/* Scrollable Body */}
                            <Box
                                flex="1"
                                overflowY="auto"
                                overflowX="auto"
                                sx={{
                                    '&::-webkit-scrollbar': {
                                        width: '6px',
                                        height: '6px',
                                        borderRadius: '8px',
                                        backgroundColor: 'rgba(0, 0, 0, 0.05)',
                                    },
                                    '&::-webkit-scrollbar-thumb': {
                                        backgroundColor: 'rgba(0, 0, 0, 0.1)',
                                        borderRadius: '8px',
                                    },
                                }}
                            >
                                <Table variant="unstyled" size="md">
                                    <Tbody>
                                        {templates.map((template) => (
                                            <Tr key={template.id} _hover={{ bg: 'gray.50' }}>
                                                <Td minW="120px">{template.date}</Td>
                                                <Td minW="200px" fontWeight="medium">{template.name}</Td>
                                                <Td minW="150px">{template.cardLinked}</Td>
                                                <Td minW="120px">
                                                    <Button
                                                        as={RouterLink}
                                                        to={`/rules/edit/${template.id}`}
                                                        variant="solid"
                                                        colorScheme="brand"
                                                        size="sm"
                                                        borderRadius="md"
                                                        px={8}
                                                        fontWeight="medium"
                                                    >
                                                        Edit
                                                    </Button>
                                                </Td>
                                            </Tr>
                                        ))}
                                    </Tbody>
                                </Table>
                            </Box>
                        </Box>
                    )}
                </CardBody>
            </Card>
        </Box>
    );
};

export default ViewTemplates;