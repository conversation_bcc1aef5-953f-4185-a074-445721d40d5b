import { create } from 'zustand';
import { useAuthStore } from './authStore';

// Types
interface CardMetric {
  name: string;
  total: number;
  approved: number;
}

interface ApplicationMetric {
  name: string;
  application: number;
  approved: number;
}

interface TrendPoint {
  value: number;
}

interface DashboardState {
  isLoading: boolean;
  error: string | null;
  totalApplications: number;
  totalApproved: number;
  applicationTrend: TrendPoint[];
  approvalTrend: TrendPoint[];
  cardMetrics: CardMetric[];
  applicationMetrics: ApplicationMetric[];
  timeframe: 'daily' | 'weekly' | 'monthly' | '3months';
  fetchDashboardData: () => Promise<void>;
  setTimeframe: (timeframe: 'daily' | 'weekly' | 'monthly' | '3months') => void;
}

// Mock data
const mockCardMetrics: CardMetric[] = [
  { name: 'HDFC', total: 50, approved: 30 },
  { name: 'Axis', total: 45, approved: 35 },
  { name: 'ICICI', total: 55, approved: 20 },
  { name: 'S<PERSON>', total: 55, approved: 20 },
];

// Mock data for 7 days (Weekly view)
const mockWeeklyApplicationMetrics: ApplicationMetric[] = [
  { name: 'Mon', application: 400, approved: 230 },
  { name: 'Tue', application: 350, approved: 280 },
  { name: 'Wed', application: 480, approved: 320 },
  { name: 'Thu', application: 420, approved: 350 },
  { name: 'Fri', application: 520, approved: 380 },
  { name: 'Sat', application: 300, approved: 200 },
  { name: 'Sun', application: 280, approved: 180 },
];

// Mock data for 12 months (Monthly view)
const mockMonthlyApplicationMetrics: ApplicationMetric[] = [
  { name: 'Jan', application: 4500, approved: 3200 },
  { name: 'Feb', application: 4200, approved: 3000 },
  { name: 'Mar', application: 4800, approved: 3600 },
  { name: 'Apr', application: 5000, approved: 3800 },
  { name: 'May', application: 5200, approved: 4000 },
  { name: 'Jun', application: 4900, approved: 3700 },
  { name: 'Jul', application: 5100, approved: 3900 },
  { name: 'Aug', application: 4700, approved: 3500 },
  { name: 'Sep', application: 5300, approved: 4100 },
  { name: 'Oct', application: 4600, approved: 3400 },
  { name: 'Nov', application: 5000, approved: 3800 },
  { name: 'Dec', application: 5400, approved: 4200 },
];

// Mock data for 3 months view (quarterly)
const mock3MonthsApplicationMetrics: ApplicationMetric[] = [
  { name: 'Q1 2024', application: 13500, approved: 9800 },
  { name: 'Q2 2024', application: 15100, approved: 11500 },
  { name: 'Q3 2024', application: 15100, approved: 11400 },
];

const mockApplicationTrend: TrendPoint[] = [
  { value: 10 },
  { value: 15 },
  { value: 13 },
  { value: 17 },
  { value: 20 },
  { value: 18 },
  { value: 22 },
  { value: 25 },
  { value: 20 },
  { value: 28 },
];

const mockApprovalTrend: TrendPoint[] = [
  { value: 5 },
  { value: 8 },
  { value: 10 },
  { value: 12 },
  { value: 15 },
  { value: 13 },
  { value: 18 },
  { value: 15 },
  { value: 17 },
  { value: 20 },
];

// Create the store
export const useDashboardStore = create<DashboardState>((set, get) => ({
  isLoading: false,
  error: null,
  totalApplications: 700,
  totalApproved: 500,
  applicationTrend: mockApplicationTrend,
  approvalTrend: mockApprovalTrend,
  cardMetrics: mockCardMetrics,
  applicationMetrics: mockWeeklyApplicationMetrics, // Default to weekly
  timeframe: 'weekly',
  
  fetchDashboardData: async () => {
    set({ isLoading: true, error: null });

    try {
      const user = useAuthStore.getState().user;

      if (!user) {
        throw new Error('User not authenticated');
      }

      const dashboard = user.dashboard || {};
      const timeframe = get().timeframe;

      // Use mock data if actual dashboard data is empty or not present
      const isMock = !dashboard.bank_wise_summary?.length && !dashboard.daily_summary?.length;

      let applicationMetrics: ApplicationMetric[] = [];

      if (timeframe === 'weekly') {
        // Use 7 days of data for weekly view
        applicationMetrics = isMock
          ? mockWeeklyApplicationMetrics
          : dashboard.weekly.map((entry: any, index: number) => ({
              name: entry.day,
              application: entry.total_applications || 0,
              approved: entry.approved_applications || 0,
            })) || mockWeeklyApplicationMetrics;
      } else if (timeframe === 'monthly') {
        // Use 12 months of data for monthly view
        applicationMetrics = isMock
          ? mockMonthlyApplicationMetrics
          : dashboard.current_month_summary.map((entry: any, index: number) => ({
              name: entry.date,
              application: entry.total_applications || 0,
              approved: entry.approved_applications || 0,
            })) || mockMonthlyApplicationMetrics;

      } else if (timeframe === '3months') {
        // Use 3 quarters of data for 3months view
        applicationMetrics = isMock
          ? mock3MonthsApplicationMetrics
          : dashboard.monthly_summary.map((entry: any, index: number) => ({
              name: entry.month,
              application: entry.total_applications || 0,
              approved: entry.approved_applications || 0,
            })) || mock3MonthsApplicationMetrics;
      }

      set({
        isLoading: false,
        totalApplications: isMock ? 700 : dashboard.total_summary?.total_applications ?? 0,
        totalApproved: isMock ? 500 : dashboard.total_summary?.total_approved ?? 0,
        applicationMetrics,
        cardMetrics: isMock
          ? mockCardMetrics
          : dashboard.bank_wise_summary?.map((entry: any) => ({
              name: entry.bank_name || 'Bank',
              total: entry.total_applications || 0,
              approved: entry.approved_applications || 0,
            })) || mockCardMetrics,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: (error as Error).message || 'Failed to load dashboard data',
      });
    }
  },
  
  // Update timeframe and fetch new data
  setTimeframe: (timeframe) => {
    set({ timeframe });
    get().fetchDashboardData();
  },
}));