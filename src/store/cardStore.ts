// src/store/cardStore.ts
import { create } from 'zustand';
import { Card, CardInput } from '../api/types';
import { useAuthStore } from './authStore';

// Mock data
const mockCards: Card[] = [
  {
    id: '1',
    title: 'Corporate Card',
    description: 'Standard corporate card with basic features',
    status: 'active',
    image: 'https://via.placeholder.com/300x200?text=Corporate+Card',
    templateId: '1',
    createdAt: '2023-01-15T10:30:00Z',
    updatedAt: '2023-02-20T14:45:00Z',
  },
  {
    id: '2',
    title: 'Premium Business Card',
    description: 'Premium card with advanced features and rewards',
    status: 'active',
    image: 'https://via.placeholder.com/300x200?text=Premium+Card',
    templateId: '2',
    createdAt: '2023-03-05T09:15:00Z',
    updatedAt: '2023-03-05T09:15:00Z',
  },
  {
    id: '3',
    title: 'Travel Rewards Card',
    description: 'Card optimized for travel benefits',
    status: 'inactive',
    image: 'https://via.placeholder.com/300x200?text=Travel+Card',
    templateId: '1',
    createdAt: '2023-04-18T11:20:00Z',
    updatedAt: '2023-05-10T16:30:00Z',
  }
];

interface CardState {
  cards: Card[];
  isLoading: boolean;
  totalCount: number;
  error: string | null;
  selectedCard: Card | null;

  // Actions
 fetchCards: (params: { page: number; limit: number }) => Promise<void>;
  fetchCard: (id: string) => Promise<void>;
  createCard: (card: CardInput) => Promise<Card>;
  updateCard: (id: string, card: Partial<CardInput>) => Promise<Card>;
  deleteCard: (id: string) => Promise<void>;
  updateCardStatus: (id: string, status: string) => Promise<Card>;
  setSelectedCard: (card: Card | null) => void;
}

// Helper to simulate async behavior
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const useCardStore = create<CardState>((set, get) => ({
  // Initial state
  cards: [...mockCards], // Start with mock data
  isLoading: false,
   totalCount: 0,
  error: null,
  selectedCard: null,

  // Mock API operations
 fetchCards: async ({ page, limit }: { page: number; limit: number }) => {
  set({ isLoading: true, error: null });

  try {
    const token = useAuthStore.getState().token;

    const response = await fetch(
      `http://bank-data-pdf-light-mode-*********.ap-south-1.elb.amazonaws.com/card/fetchcard?page=${page}&limit=${limit}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch cards');
    }

    const data = await response.json();

    const normalizedCards: Card[] = data.cards.map((card: any) => ({
      id: card.id,
      title: card.card_name,
      description: card.description,
      status: card.status,
      image: card.card_image_url,
      templateId: card.template_id,
      createdAt: card.created_at,
      updatedAt: card.updated_at
    }));

    set({
      cards: normalizedCards,
      isLoading: false,
      totalCount: data.total // 👈 Add this field in your store if not yet added
    });
  } catch (error) {
    set({
      error: error instanceof Error ? error.message : 'Failed to fetch cards',
      isLoading: false
    });
  }
},

  fetchCard: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      await delay(600);
      const card = get().cards.find(c => c.id == id);
      if (!card) {
        throw new Error('Card not found');
      }
      set({ selectedCard: card, isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch card',
        isLoading: false
      });
    }
  },

 createCard: async (cardData: CardInput) => {
  set({ isLoading: true, error: null });

  try {
    const token = useAuthStore.getState().token;

    // Build FormData
    const formData = new FormData();
    formData.append('card_name', cardData.name);
    formData.append('sub_header', cardData.subHeader);
    formData.append('offer', '5% on groceries');
    formData.append('description', cardData.description);
    formData.append('utm_link', cardData.utmLink);
    formData.append('rule_id', '1');
    formData.append('earning', cardData.earning.toString());
    formData.append('status', cardData.status);
    formData.append('bank_name', 'ABC Bank');
    formData.append('card_type', 'Credit');

    // Important: card_image must be a File object (from input type="file")
    formData.append('card_image', cardData.imageUrl);

    const response = await fetch('http://bank-data-pdf-light-mode-*********.ap-south-1.elb.amazonaws.com/card/create', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
        // DO NOT manually set 'Content-Type': multipart/form-data
        // The browser will automatically set the correct boundary
      },
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create card');
    }

    const data = await response.json();

    // Handle as needed
    console.log("Card created:", data);
    return data;

  } catch (error) {
    set({
      error: error instanceof Error ? error.message : 'Failed to create card',
      isLoading: false
    });
    throw error;
  }
},

updateCard: async (id: string, cardData: Partial<CardInput>) => {
  set({ isLoading: true, error: null });

  try {
    const token = useAuthStore.getState().token;

    // Convert `CardInput` structure to API expected fields
    const payload = {
      id: Number(id),
      card_name: cardData.name || '',
      description: cardData.description || '',
      sub_header: cardData.subHeader || '',
      utm_link: cardData.utmLink || '', // You can store this in cardData if needed
      card_image_url: cardData.imageUrl || '',
      bank_name: 'ABC Bank', // Make dynamic if required
      card_type: 'Credit',
      points: cardData.earning // Make dynamic if needed
    };

    const response = await fetch('http://bank-data-pdf-light-mode-*********.ap-south-1.elb.amazonaws.com/card/update', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update card');
    }

    const updatedData = await response.json();

    const cardIndex = get().cards.findIndex(c => c.id === id);
    if (cardIndex === -1) throw new Error('Card not found');

    const updatedCard: Card = {
      ...get().cards[cardIndex],
      ...cardData,
      updatedAt: new Date().toISOString()
    };

    const updatedCards = [...get().cards];
    updatedCards[cardIndex] = updatedCard;

    set({
      cards: updatedCards,
      selectedCard: get().selectedCard?.id === id ? updatedCard : get().selectedCard,
      isLoading: false
    });

    return updatedCard;

  } catch (error) {
    set({
      error: error instanceof Error ? error.message : 'Failed to update card',
      isLoading: false
    });
    throw error;
  }
},

  deleteCard: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      await delay(700);
      set(state => ({
        cards: state.cards.filter(card => card.id !== id),
        selectedCard: state.selectedCard?.id === id ? null : state.selectedCard,
        isLoading: false
      }));
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to delete card',
        isLoading: false
      });
      throw error;
    }
  },

  updateCardStatus: async (id: string, status: string) => {
    set({ isLoading: true, error: null });
    try {
      await delay(500);
      return await get().updateCard(id, { status: status as any });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to update card status',
        isLoading: false
      });
      throw error;
    }
  },

  setSelectedCard: (card: Card | null) => {
    set({ selectedCard: card });
  }
}));