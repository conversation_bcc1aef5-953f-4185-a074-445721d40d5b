import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useAuthStore } from './authStore';

export interface FetchApplicationsParams {
  page: number;
  limit: number;
}

export interface ApplicationsResponse {
  applications: Application[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
}

export interface Application {
  id: string;
  date: string;
  mCode: string;
  name: string;
  mobile: string;
  score: number;
  status: string;
  fullName: string;
  employmentType: string;
  salaryRange: string;
  pan: string;
  creditScore: number;
  pincode: string;
  cardApplied: string;
  merchantCode: string;
  currentStep: number;
}

// Define the application store type
type ApplicationStore = {
  applications: Application[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  isLoading: boolean;
  error: string | null;
  fetchApplications: (params: FetchApplicationsParams) => Promise<void>;
  getApplicationById: (id?: string) => Application | undefined;
  updateApplicationStatus: (id: string, status: string, step: number) => Promise<void>;
};

// Create the Zustand store with real API integration
export const useApplicationStore = create<ApplicationStore>()(
  persist(
    (set, get) => ({
      applications: [],
      totalCount: 0,
      currentPage: 1,
      totalPages: 0,
      isLoading: false,
      error: null,
      
      // Fetch applications with pagination support
      fetchApplications: async (params: FetchApplicationsParams) => {
        set({ isLoading: true, error: null });
      
        try {
          const token = useAuthStore.getState().token;
          
          if (!token) {
            throw new Error('No authentication token available');
          }

          const response = await fetch(
            'http://bank-data-pdf-light-mode-*********.ap-south-1.elb.amazonaws.com/card/admin_leads',
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`
              },
              body: JSON.stringify({
                page: params.page,
                limit: params.limit
              })
            }
          );
      
          if (!response.ok) {
            throw new Error(`API error: ${response.status} ${response.statusText}`);
          }
      
          const data = await response.json();
      
          // Map the backend response to your frontend format
          const mappedApplications = data.leads.map((lead: any, index: number) => ({
            id: lead.txn_id || ((params.page - 1) * params.limit + index + 1).toString(),
            key: lead.id,
            date: lead.created_at ? lead.created_at.split(' ')[0] : '',
            mCode: lead.agent_name || '',
            name: lead.name || '',
            mobile: lead.phone_no || '',
            score: lead.score || 0,
            status: lead.card_status || 'Unknown',
            fullName: lead.name || '',
            employmentType: lead.employment_type || '',
            salaryRange: lead.salary_range || '',
            pan: lead.pan_no || '',
            creditScore: lead.credit_score || 0,
            pincode: lead.pin_code || '',
            cardApplied: lead.card_name || '',
            merchantCode: lead.merchant_code || '',
            currentStep: lead.current_step || 0
          }));

          const totalCount = data.total_count || data.leads.length;
          const totalPages = Math.ceil(totalCount / params.limit);
      
          set({
            applications: mappedApplications,
            totalCount,
            currentPage: params.page,
            totalPages,
            isLoading: false
          });
        } catch (error) {
          console.error('Fetch applications error:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch applications. Please try again later.',
            isLoading: false
          });
        }
      },
      
      // Get application by ID (searches in current page)
      getApplicationById: (id) => {
        if (!id) return undefined;
        return get().applications.find(app => app.id === id);
      },
      
      // Update application status
      updateApplicationStatus: async (id, status, step) => {
        set({ isLoading: true, error: null });
        
        try {
          const token = useAuthStore.getState().token;
          
          if (!token) {
            throw new Error('No authentication token available');
          }

          // Make API call to update status
          const response = await fetch(
            'http://bank-data-pdf-light-mode-*********.ap-south-1.elb.amazonaws.com/card/update_application_status',
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`
              },
              body: JSON.stringify({
                application_id: id,
                status,
                current_step: step
              })
            }
          );

          if (!response.ok) {
            throw new Error(`Failed to update status: ${response.status} ${response.statusText}`);
          }
          
          // Update local state after successful API call
          set(state => ({
            applications: state.applications.map(app => 
              app.id === id 
                ? { ...app, status, currentStep: step } 
                : app
            ),
            isLoading: false
          }));
        } catch (error) {
          console.error('Update application status error:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update application status. Please try again later.', 
            isLoading: false 
          });
        }
      }
    }),
    {
      name: 'application-store',
      partialize: (state) => ({ 
        // Cache applications data but not loading/error states
        applications: state.applications,
        totalCount: state.totalCount,
        currentPage: state.currentPage,
        totalPages: state.totalPages
      }),
    }
  )
);